#!/bin/bash

# Phish X Deployment Script

# Set the project name
PROJECT_NAME="aiphish"

# Set the output directory
OUTPUT_DIR="./dist"

# Set the main binary name
MAIN_BINARY_NAME="aiphish"

# Set the agent binary name
AGENT_BINARY_NAME="agent"

# Create the output directory if it doesn't exist
mkdir -p $OUTPUT_DIR

# Build the main application
echo "Building the main application..."
go build -o $OUTPUT_DIR/$MAIN_BINARY_NAME .

# Check if the main build was successful
if [ $? -ne 0 ]; then
    echo "Main application build failed!"
    exit 1
fi

# Build the agent
echo "Building the agent..."
cd agent
go build -o ../$OUTPUT_DIR/$AGENT_BINARY_NAME .
cd ..

# Check if the agent build was successful
if [ $? -ne 0 ]; then
    echo "Agent build failed!"
    exit 1
fi

# Copy necessary files and directories
echo "Copying necessary files and directories..."

# Copy the web directory
cp -r ./web $OUTPUT_DIR/

# Copy the plugins directory
cp -r ./plugins $OUTPUT_DIR/

# Copy the docs directory
cp -r ./docs $OUTPUT_DIR/

# Copy the README.md file
cp ./README.md $OUTPUT_DIR/

# Create a simple start script for main application on Linux
echo "#!/bin/bash" > $OUTPUT_DIR/start.sh
echo "cd \$(dirname \$0)" >> $OUTPUT_DIR/start.sh
echo "./$MAIN_BINARY_NAME" >> $OUTPUT_DIR/start.sh
chmod +x $OUTPUT_DIR/start.sh

# Create a simple start script for agent on Linux
echo "#!/bin/bash" > $OUTPUT_DIR/start-agent.sh
echo "cd \$(dirname \$0)" >> $OUTPUT_DIR/start-agent.sh
echo "./$AGENT_BINARY_NAME" >> $OUTPUT_DIR/start-agent.sh
chmod +x $OUTPUT_DIR/start-agent.sh

# Create a simple start script for main application on Windows
echo "@echo off" > $OUTPUT_DIR/start.bat
echo "cd /d %~dp0" >> $OUTPUT_DIR/start.bat
echo "$MAIN_BINARY_NAME.exe" >> $OUTPUT_DIR/start.bat

# Create a simple start script for agent on Windows
echo "@echo off" > $OUTPUT_DIR/start-agent.bat
echo "cd /d %~dp0" >> $OUTPUT_DIR/start-agent.bat
echo "$AGENT_BINARY_NAME.exe" >> $OUTPUT_DIR/start-agent.bat

# Print the deployment summary
echo "Deployment completed successfully!"
echo "The main application and agent have been packaged in the $OUTPUT_DIR directory."
echo "To start the main application, run start.sh (Linux) or start.bat (Windows) from the $OUTPUT_DIR directory."
echo "To start the agent, run start-agent.sh (Linux) or start-agent.bat (Windows) from the $OUTPUT_DIR directory."