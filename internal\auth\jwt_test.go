package auth

import (
	"testing"
	"time"
)

func TestGenerateToken(t *testing.T) {
	// Test token generation
	tokenString, err := GenerateToken("testuser", "<EMAIL>")
	if err != nil {
		t.<PERSON>rrorf("Failed to generate token: %v", err)
	}

	// Check that token is not empty
	if tokenString == "" {
		t.Error("Generated token is empty")
	}
}

func TestParseToken(t *testing.T) {
	// Generate a token
	tokenString, err := GenerateToken("testuser", "<EMAIL>")
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// Parse the token
	claims, err := ParseToken(tokenString)
	if err != nil {
		t.Errorf("Failed to parse token: %v", err)
	}

	// Check claims
	if claims.Username != "testuser" {
		t.<PERSON>rrorf("Expected username testuser, got %s", claims.Username)
	}

	if claims.Email != "<EMAIL>" {
		t.<PERSON>rf("<NAME_EMAIL>, got %s", claims.Email)
	}

	// Check token expiration
	if claims.ExpiresAt.Unix() <= time.Now().Unix() {
		t.<PERSON>rrorf("Token has already expired")
	}
}

func TestParseInvalidToken(t *testing.T) {
	// Test invalid token
	_, err := ParseToken("invalid.token.string")
	if err == nil {
		t.Errorf("Expected error when parsing invalid token")
	}
}