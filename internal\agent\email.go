package agent

import (
	"crypto/tls"
	"fmt"
	"log"
	"net/smtp"
)

// EmailAgent handles email sending
type <PERSON>ail<PERSON><PERSON> struct {
	SMTPHost string
	SMTPPort string
	Username string
	Password string
}

// NewEmailAgent creates a new email agent
func NewEmailAgent(host, port, username, password string) *EmailAgent {
	return &EmailAgent{
		SMTPHost: host,
		SMTPPort: port,
		Username: username,
		Password: password,
	}
}

// SendMail sends an email
func (e *EmailAgent) SendMail(from, to, subject, body string) error {
	log.Printf("Sending email from %s to %s with subject: %s", from, to, subject)
	
	addr := fmt.Sprintf("%s:%s", e.SMTPHost, e.SMTPPort)
	auth := smtp.PlainAuth("", e.Username, e.Password, e.SMTPHost)

	// Connect to the server, authenticate, set the sender and recipient,
	// and send the email all in one step.
	msg := []byte("To: " + to + "\r\n" +
		"Subject: " + subject + "\r\n" +
		"\r\n" +
		body + "\r\n")

	// TLS config
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		ServerName:         e.SMTPHost,
	}

	// Connect to server
	conn, err := tls.Dial("tcp", addr, tlsConfig)
	if err != nil {
		log.Printf("Failed to connect to SMTP server: %v", err)
		return err
	}
	defer func() {
		if closeErr := conn.Close(); closeErr != nil {
			log.Printf("Error closing SMTP connection: %v", closeErr)
		}
	}()

	// Create SMTP client
	client, err := smtp.NewClient(conn, e.SMTPHost)
	if err != nil {
		log.Printf("Failed to create SMTP client: %v", err)
		return err
	}
	defer func() {
		if quitErr := client.Quit(); quitErr != nil {
			log.Printf("Error quitting SMTP client: %v", quitErr)
		}
	}()

	// Auth
	if err = client.Auth(auth); err != nil {
		log.Printf("Failed to authenticate with SMTP server: %v", err)
		return err
	}

	// Set sender
	if err = client.Mail(from); err != nil {
		log.Printf("Failed to set sender: %v", err)
		return err
	}

	// Set recipient
	if err = client.Rcpt(to); err != nil {
		log.Printf("Failed to set recipient: %v", err)
		return err
	}

	// Send email body
	writer, err := client.Data()
	if err != nil {
		log.Printf("Failed to create data writer: %v", err)
		return err
	}
	
	_, err = writer.Write(msg)
	if err != nil {
		log.Printf("Failed to write email body: %v", err)
		// Close writer even if write failed
		if closeErr := writer.Close(); closeErr != nil {
			log.Printf("Error closing data writer: %v", closeErr)
		}
		return err
	}
	
	err = writer.Close()
	if err != nil {
		log.Printf("Failed to close data writer: %v", err)
		return err
	}

	log.Printf("Email sent successfully from %s to %s", from, to)
	return nil
}