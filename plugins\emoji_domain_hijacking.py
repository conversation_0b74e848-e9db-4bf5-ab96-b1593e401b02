import json
import sys
import unicodedata

def get_info():
    # Return plugin information
    info = {
        "name": "emoji_domain_hijacking",
        "description": "表情符号域名劫持插件，通过注册包含表情符号的相似域名实现欺骗",
        "version": "1.0.0"
    }
    return info

def generate_emoji_domain(target_domain, emoji_char):
    """
    Generate an emoji domain by prepending an emoji to the target domain
    Returns both the Unicode domain and its Punycode representation
    """
    # Create the emoji domain
    emoji_domain = f"{emoji_char}-{target_domain}"
    
    # Convert to Punycode
    try:
        punycode_domain = emoji_domain.encode('idna').decode('ascii')
        return emoji_domain, punycode_domain
    except Exception as e:
        # If IDNA encoding fails, use a simple ASCII prefix
        ascii_domain = f"secure-{target_domain}"
        punycode_domain = ascii_domain.encode('idna').decode('ascii')
        return ascii_domain, punycode_domain

def run(target, email, params):
    # Get action from params
    action = params.get("action")
    
    # If action is 'info', return plugin information
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # Emoji domain hijacking logic
    try:
        # Get parameters
        target_domain = params.get("target_domain", "target.com")
        emoji_char = params.get("emoji_char", "secure")  # Use ASCII prefix as default
        
        # Generate emoji domain
        emoji_domain, punycode_domain = generate_emoji_domain(target_domain, emoji_char)
        
        # Create a copy of the email data to avoid modifying the original
        modified_email = email.copy()
        
        # Modify the from address to use the emoji domain
        # Extract the username part from the original from address
        original_from = email.get("from", "<EMAIL>")
        if "@" in original_from:
            username = original_from.split("@")[0]
            modified_email["from"] = f"{username}@{emoji_domain}"
        else:
            modified_email["from"] = f"admin@{emoji_domain}"
        
        # Add a comment to the email body to indicate emoji domain hijacking
        modified_email["body"] = f"{modified_email.get('body', '')}\n\n<!-- Emoji Domain Hijacking Plugin Applied -->"
        
        result = {
            "status": "success",
            "email": modified_email,
            "detail": f"Domain generated: {emoji_domain} (Punycode: {punycode_domain})"
        }
        return result
    except Exception as e:
        result = {
            "status": "error",
            "detail": f"Failed to apply emoji domain hijacking: {str(e)}"
        }
        return result

if __name__ == "__main__":
    # Read input from stdin
    data = json.loads(sys.stdin.read())
    
    # Extract target, email and parameters
    target = data.get("target")
    email = data.get("email", {})
    params = data.get("params", {})
    
    # Run the plugin logic
    res = run(target, email, params)
    
    # Output result to stdout
    print(json.dumps(res))