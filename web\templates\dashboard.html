<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据展示 - Phish X</title>
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Materialize CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --accent-color: #FF9800;
            --warn-color: #F44336;
            --background-color: #f5f5f5;
            --surface-color: #ffffff;
            --text-primary: rgba(0, 0, 0, 0.87);
            --text-secondary: rgba(0, 0, 0, 0.54);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .sidenav {
            background-color: var(--surface-color);
        }

        .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            margin-bottom: 1rem;
        }

        .card-content {
            padding: 16px;
        }

        .btn-primary {
            background-color: var(--primary-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-accent {
            background-color: var(--accent-color);
        }

        .btn-warn {
            background-color: var(--warn-color);
        }

        .page-header {
            padding: 1rem 0;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(0,0,0,.1);
        }

        .page-title {
            font-weight: 500;
            margin: 0;
        }

        .breadcrumb {
            font-size: 1rem;
        }

        .breadcrumb:before {
            color: var(--text-secondary);
        }

        .breadcrumb:last-child {
            color: var(--text-primary);
        }

        .table-responsive {
            overflow-x: auto;
        }

        .data-table th {
            font-weight: 500;
            color: var(--text-primary);
        }

        .data-table td {
            color: var(--text-secondary);
        }

        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-primary {
            color: #fff;
            background-color: var(--primary-color);
        }

        .badge-secondary {
            color: #fff;
            background-color: var(--secondary-color);
        }

        .badge-accent {
            color: #fff;
            background-color: var(--accent-color);
        }

        .badge-warn {
            color: #fff;
            background-color: var(--warn-color);
        }

        .badge-success {
            color: #fff;
            background-color: #4CAF50;
        }

        .badge-danger {
            color: #fff;
            background-color: #F44336;
        }

        .badge-warning {
            color: #fff;
            background-color: #FF9800;
        }

        .loader {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .toast {
            border-radius: 4px;
        }

        .footer {
            padding: 1rem 0;
            background-color: var(--surface-color);
            border-top: 1px solid rgba(0,0,0,.1);
        }

        .footer-links a {
            color: var(--text-secondary);
            margin-right: 1rem;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-wrapper">
            <a href="/" class="brand-logo">
                <i class="material-icons left" style="margin-left: 10px;">security</i>
                Phish X
            </a>
            <a href="#" data-target="mobile-nav" class="sidenav-trigger">
                <i class="material-icons">menu</i>
            </a>
            <ul class="right hide-on-med-and-down">
                <li><a href="/tasks">任务管理</a></li>
                <li><a href="/plugin-management">插件管理</a></li>
                <li><a href="/email-test">邮件测试</a></li>
                <li><a href="/scheduler">任务调度</a></li>
                <li class="active"><a href="/dashboard">数据展示</a></li>
                <li><a href="/agent-management">Agent管理</a></li>
                <li id="authSection">
                    <!-- Auth buttons will be inserted here by JavaScript -->
                </li>
            </ul>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <ul class="sidenav" id="mobile-nav">
        <li><a href="/tasks">任务管理</a></li>
        <li><a href="/plugin-management">插件管理</a></li>
        <li><a href="/email-test">邮件测试</a></li>
        <li><a href="/scheduler">任务调度</a></li>
        <li class="active"><a href="/dashboard">数据展示</a></li>
        <li><a href="/agent-management">Agent管理</a></li>
        <li id="mobileAuthSection">
            <!-- Auth buttons will be inserted here by JavaScript -->
        </li>
    </ul>

    <!-- Breadcrumbs -->
    <nav class="breadcrumb-container">
        <div class="nav-wrapper">
            <div class="col s12">
                <a href="/" class="breadcrumb">首页</a>
                <a href="/dashboard" class="breadcrumb">数据展示</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="page-header">
                        <h2 class="page-title">数据展示</h2>
                        <p class="lead">查看任务和插件信息。</p>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">任务列表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>描述</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tasksTableBody">
                                        <!-- Tasks will be loaded here by JavaScript -->
                                        <tr>
                                            <td colspan="4" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">插件列表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>版本</th>
                                            <th>目标</th>
                                        </tr>
                                    </thead>
                                    <tbody id="pluginsTableBody">
                                        <!-- Plugins will be loaded here by JavaScript -->
                                        <tr>
                                            <td colspan="4" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">调度任务列表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>任务ID</th>
                                            <th>任务名称</th>
                                            <th>状态</th>
                                            <th>执行间隔</th>
                                            <th>下次执行</th>
                                            <th>上次执行</th>
                                            <th>执行次数</th>
                                        </tr>
                                    </thead>
                                    <tbody id="scheduledTasksTableBody">
                                        <!-- Scheduled tasks will be loaded here by JavaScript -->
                                        <tr>
                                            <td colspan="7" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="page-footer">
        <div class="container">
            <div class="row">
                <div class="col l6 s12">
                    <h5 class="white-text">Phish X</h5>
                    <p class="grey-text text-lighten-4">一个轻便型、插件化、可扩展的社会工程安全测试平台。</p>
                </div>
                <div class="col l4 offset-l2 s12">
                    <h5 class="white-text">链接</h5>
                    <ul class="footer-links">
                        <li><a class="grey-text text-lighten-3" href="/">首页</a></li>
                        <li><a class="grey-text text-lighten-3" href="/docs">文档</a></li>
                        <li><a class="grey-text text-lighten-3" href="/support">支持</a></li>
                        <li><a class="grey-text text-lighten-3" href="/about">关于</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-copyright">
            <div class="container">
                © 2023 Phish X. 保留所有权利。
                <a class="grey-text text-lighten-4 right" href="#!">隐私政策</a>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Materialize components
            $('.sidenav').sidenav();
            
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (!token) {
                // Redirect to login page if not logged in
                window.location.href = '/login';
                return;
            }
            
            // Show user info and logout button
            $('#authSection').html(`
                <li class="nav-item">
                    <a class="nav-link waves-effect waves-light btn red lighten-1" href="#" id="logoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#mobileAuthSection').html(`
                <li>
                    <a href="#" id="mobileLogoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#logoutBtn, #mobileLogoutBtn').click(function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                window.location.href = '/login';
            });
            
            // Load dashboard data
            loadDashboardData();
        });
        
        function loadDashboardData() {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: '/api/v1/dashboard/data',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(data) {
                    // Update tables
                    updateTasksTable(data.tasks);
                    updatePluginsTable(data.plugins);
                    updateScheduledTasksTable(data.scheduled_tasks);
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        alert('登录已过期，请重新登录');
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        console.log('加载仪表板数据失败: ' + xhr.responseJSON.error);
                    }
                }
            });
        }
        
        function updateTasksTable(tasks) {
            let tasksHtml = '';
            tasks.forEach(task => {
                tasksHtml += `
                    <tr>
                        <td>${task.ID}</td>
                        <td>${task.Name}</td>
                        <td>${task.Description || ''}</td>
                        <td><span class="badge badge-${task.Status === 'completed' ? 'success' : task.Status === 'failed' ? 'danger' : 'warning'}">${task.Status}</span></td>
                    </tr>
                `;
            });
            
            if (tasks.length === 0) {
                tasksHtml = '<tr><td colspan="4" class="text-center">暂无任务</td></tr>';
            }
            
            $('#tasksTableBody').html(tasksHtml);
        }
        
        function updatePluginsTable(plugins) {
            let pluginsHtml = '';
            plugins.forEach(plugin => {
                pluginsHtml += `
                    <tr>
                        <td>${plugin.ID}</td>
                        <td>${plugin.Name}</td>
                        <td>${plugin.Version}</td>
                        <td>${plugin.Target}</td>
                    </tr>
                `;
            });
            
            if (plugins.length === 0) {
                pluginsHtml = '<tr><td colspan="4" class="text-center">暂无插件</td></tr>';
            }
            
            $('#pluginsTableBody').html(pluginsHtml);
        }
        
        function updateScheduledTasksTable(scheduledTasks) {
            let tasksHtml = '';
            scheduledTasks.forEach(task => {
                // Format interval
                let intervalStr = '';
                if (task.Interval < 60) {
                    intervalStr = `${task.Interval}秒`;
                } else if (task.Interval < 3600) {
                    intervalStr = `${Math.floor(task.Interval / 60)}分钟`;
                } else if (task.Interval < 86400) {
                    intervalStr = `${Math.floor(task.Interval / 3600)}小时`;
                } else {
                    intervalStr = `${Math.floor(task.Interval / 86400)}天`;
                }
                
                tasksHtml += `
                    <tr>
                        <td>${task.TaskID}</td>
                        <td>${task.Name}</td>
                        <td><span class="badge badge-${task.Status}">${task.Status}</span></td>
                        <td>${intervalStr}</td>
                        <td>${task.NextExecution ? new Date(task.NextExecution).toLocaleString('zh-CN') : 'N/A'}</td>
                        <td>${task.LastExecution ? new Date(task.LastExecution).toLocaleString('zh-CN') : 'N/A'}</td>
                        <td>${task.CurrentCount}</td>
                    </tr>
                `;
            });
            
            if (scheduledTasks.length === 0) {
                tasksHtml = '<tr><td colspan="7" class="text-center">暂无调度任务</td></tr>';
            }
            
            $('#scheduledTasksTableBody').html(tasksHtml);
        }
    </script>
</body>
</html>