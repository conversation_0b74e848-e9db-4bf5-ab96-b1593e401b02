import json
import sys

def get_info():
    # Return plugin information
    info = {
        "name": "spf_bypass",
        "description": "SPF绕过插件，通过分离Mail From和From字段实现邮件伪造",
        "version": "1.0.0"
    }
    return info

def run(target, email, params):
    # Get action from params
    action = params.get("action")
    
    # If action is 'info', return plugin information
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # SPF bypass logic
    # Get the desired Mail From address from params
    mail_from = params.get("mail_from", email.get("from", "<EMAIL>"))
    
    # Create a copy of the email data to avoid modifying the original
    modified_email = email.copy()
    
    # Add the Mail From address to headers so it can be used by the agent
    # The From header in the email body remains unchanged
    if "headers" not in modified_email:
        modified_email["headers"] = {}
    modified_email["headers"]["X-SPF-Bypass-Mail-From"] = mail_from
    
    # Add a comment to the email body to indicate SPF bypass
    modified_email["body"] = f"{modified_email.get('body', '')}\n\n<!-- SPF Bypass Plugin Applied -->"
    
    result = {
        "status": "success",
        "email": modified_email,
        "detail": f"SPF bypass applied. Mail From set to {mail_from}"
    }
    return result

if __name__ == "__main__":
    # Read input from stdin
    data = json.loads(sys.stdin.read())
    
    # Extract target, email and parameters
    target = data.get("target")
    email = data.get("email", {})
    params = data.get("params", {})
    
    # Run the plugin logic
    res = run(target, email, params)
    
    # Output result to stdout
    print(json.dumps(res))