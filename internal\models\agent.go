package models

// Agent represents an email sending agent in the system
type Agent struct {
	ID          string `gorm:"primaryKey" json:"id"`
	APIEndpoint string `gorm:"not null" json:"api_endpoint"`
	Status      string `gorm:"not null;default:'unknown'" json:"status"` // unknown, idle, sending, disconnected, error
	CreatedAt   int64  `json:"created_at"`                               // Unix timestamp
	LastSeen    int64  `json:"last_seen"`                                // Unix timestamp
}