package models

import "time"

// Task represents a task in the system
type Task struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Name        string    `gorm:"not null" json:"name"`
	Description string    `json:"description"`
	Status      string    `gorm:"not null;default:'pending'" json:"status"` // pending, running, completed, failed
	TaskData    string    `json:"task_data"`                                 // JSON representation of the complete task
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}