import json
import sys

def get_info():
    # Return plugin information
    info = {
        "name": "email_enrichment",
        "description": "Enriches email with tracking headers and attachments",
        "version": "1.0.0",
        "type": "email_processor"
    }
    return info

def process_email(email_data):
    # Enrich email with tracking headers
    if email_data.get("headers") is None:
        email_data["headers"] = {}
    
    # Add tracking headers
    email_data["headers"]["X-AI-Phish-Tracking"] = email_data.get("tracking_id", "unknown")
    email_data["headers"]["X-Mailer"] = "Phish X v1.0"
    
    # Add tracking pixel to body if not already present
    if "<img" not in email_data["body"]:
        tracking_pixel = f'<img src="http://tracking.example.com/pixel/{email_data.get("tracking_id", "unknown")}" width="1" height="1" />'
        email_data["body"] = email_data["body"] + tracking_pixel
    
    return email_data

def run(target, email, params):
    # Email enrichment plugin logic
    action = params.get("action")
    
    # If action is 'info', return plugin information
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # Otherwise, process the email
    try:
        enriched_email = process_email(email)
        result = {
            "status": "success",
            "email": enriched_email,
            "detail": "Email enriched successfully"
        }
        return result
    except Exception as e:
        result = {
            "status": "error",
            "detail": f"Failed to enrich email: {str(e)}"
        }
        return result

if __name__ == "__main__":
    # Read input from stdin
    data = json.loads(sys.stdin.read())
    
    # Extract target, email and parameters
    target = data.get("target")
    email = data.get("email", {})
    params = data.get("params", {})
    
    # Run the plugin logic
    res = run(target, email, params)
    
    # Output result to stdout
    print(json.dumps(res))