package main

import (
	"bytes"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net"
	"net/http"
	"net/smtp"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// EmailData represents email data
type EmailData struct {
	From       string            `json:"from"`
	To         []string          `json:"to"`
	Subject    string            `json:"subject"`
	Body       string            `json:"body"`
	Headers    map[string]string `json:"headers"`
	TrackingID string            `json:"tracking_id"`
}

// SMTPConfig holds SMTP configuration
type SMTPConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
}

// Attachment represents an email attachment
type Attachment struct {
	Filename    string `json:"filename"`
	Content     string `json:"content"`
	ContentType string `json:"content_type"`
}

// AgentStats represents agent statistics
type AgentStats struct {
	TotalSent    int64     `json:"total_sent"`
	TotalFailed  int64     `json:"total_failed"`
	TotalQueued  int64     `json:"total_queued"`
	LastSendTime time.Time `json:"last_send_time"`
	AvgSendTime  string    `json:"avg_send_time"`
	SuccessRate  float64   `json:"success_rate"`
}

// SMTPResult holds server response for each SMTP command
type SMTPResult struct {
	Stage      string `json:"stage"`
	StatusCode int    `json:"status_code"`
	Message    string `json:"message"`
}

// AgentConfig holds agent configuration
type AgentConfig struct {
	ID     string
	BaseIP string
}

var agentConfig AgentConfig

func init() {
	// Set agent ID from environment variable or use default
	agentID := os.Getenv("AGENT_ID")
	if agentID == "" {
		agentID = "simple-agent-001"
	}
	agentConfig.ID = agentID

	// Set base IP from environment variable or use default
	baseIP := os.Getenv("AGENT_BASE_IP")
	if baseIP == "" {
		baseIP = "127.0.0.1:8080"
	}
	agentConfig.BaseIP = baseIP
}

func main() {
	// Set random seed
	rand.Seed(time.Now().UnixNano())

	// Define command line flags
	agentID := flag.String("id", "simple-agent-001", "Agent ID")
	port := flag.String("port", "8081", "Agent port")
	baseIP := flag.String("baseip", "127.0.0.1:8080", "Base IP for logging")
	flag.Parse()

	// Set agent ID from command line flag, environment variable, or default
	if *agentID != "simple-agent-001" {
		agentConfig.ID = *agentID
	} else {
		envAgentID := os.Getenv("AGENT_ID")
		if envAgentID != "" {
			agentConfig.ID = envAgentID
		} else {
			agentConfig.ID = "simple-agent-001"
		}
	}

	// Set base IP from command line flag, environment variable, or default
	if *baseIP != "127.0.0.1:8080" {
		agentConfig.BaseIP = *baseIP
	} else {
		envBaseIP := os.Getenv("AGENT_BASE_IP")
		if envBaseIP != "" {
			agentConfig.BaseIP = envBaseIP
		} else {
			agentConfig.BaseIP = "127.0.0.1:8080"
		}
	}

	// Create router
	router := gin.Default()

	// API routes
	api := router.Group("/api/v1")
	{
		// Agent info
		api.GET("/info", apiGetAgentInfo)

		// Email sending
		api.POST("/send", apiSendEmail)

		// Agent status
		api.GET("/status", apiGetAgentStatus)

		// Agent stats
		api.GET("/stats", apiGetAgentStats)

		// log updatae
		api.GET("/log33398", apiGetAgentLog)
	}

	// Start server
	log.Printf("Starting simple agent server on port %s with ID %s", *port, agentConfig.ID)
	err := router.Run(":" + *port)
	if err != nil {
		log.Fatal("Failed to start simple agent server:", err)
	}
}

// apiGetAgentInfo returns agent information
func apiGetAgentInfo(c *gin.Context) {
	info := struct {
		ID      string `json:"id"`
		Version string `json:"version"`
		Status  string `json:"status"`
		Type    string `json:"type"`
	}{
		ID:      agentConfig.ID,
		Version: "1.0.0",
		Status:  "idle",
		Type:    "simple",
	}

	log.Printf("Retrieved simple agent info")
	c.JSON(http.StatusOK, info)
}

// apiSendEmail sends an email through the agent
func apiSendEmail(c *gin.Context) {
	// Parse request body
	var req struct {
		From       string            `json:"from" binding:"required"`
		To         []string          `json:"to" binding:"required"`
		Subject    string            `json:"subject" binding:"required"`
		Body       string            `json:"body" binding:"required"`
		Headers    map[string]string `json:"headers"`
		TrackingID string            `json:"tracking_id"`
		Plugins    []string          `json:"plugins"`
		SMTPConfig *SMTPConfig       `json:"smtp_config"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind email request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if SMTP configuration is provided
	if req.SMTPConfig == nil {
		log.Printf("Missing SMTP configuration")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing SMTP configuration"})
		return
	}

	// Send email using SMTP
	emailRequest := &EmailData{
		From:       req.From,
		To:         req.To,
		Subject:    req.Subject,
		Body:       req.Body,
		Headers:    req.Headers,
		TrackingID: req.TrackingID,
	}

	// Log the received data for debugging
	log.Printf("Received email request:")
	log.Printf("  From: %s", req.From)
	log.Printf("  To: %v", req.To)
	log.Printf("  Subject: %s", req.Subject)
	log.Printf("  Body length: %d", len(req.Body))
	log.Printf("  Tracking ID: %s", req.TrackingID)

	if req.SMTPConfig != nil {
		log.Printf("Received SMTP config:")
		log.Printf("  Host: %s", req.SMTPConfig.Host)
		log.Printf("  Port: %s", req.SMTPConfig.Port)
		log.Printf("  Username: %s", req.SMTPConfig.Username)
		// Don't log password for security reasons
	} else {
		log.Printf("No SMTP config received")
	}

	// Send the email using the provided SMTP configuration
	results, err := SendEmailWithSMTP(req.SMTPConfig, emailRequest)
	if err != nil {
		log.Printf("Failed to send email: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error(), "smtp_status": results})
		return
	}

	log.Printf("Email sent successfully")
	c.JSON(http.StatusOK, gin.H{"message": "Email sent", "smtp_status": results})
}

// SendEmailWithSMTP sends an email using SMTP
func SendEmailWithSMTP(config *SMTPConfig, email *EmailData) ([]SMTPResult, error) {
	hostPort := net.JoinHostPort(config.Host, config.Port)
	log.Printf("Sending email to %v via %s", email.To, hostPort)

	switch config.Port {
	case "465":
		return sendEmailWithTLS(config, email, hostPort)
	case "587", "25":
		// Try STARTTLS first, fallback to plain if fail
		results, err := sendEmailWithStartTLS(config, email, hostPort)
		if err != nil && strings.Contains(err.Error(), "starttls_failed:") {
			log.Printf("STARTTLS failed, fallback to plain SMTP: %v", err)
			return sendEmailPlain(config, email, hostPort)
		}
		return results, err
	default:
		// Other ports: try TLS → STARTTLS → Plain
		results, err := sendEmailWithTLS(config, email, hostPort)
		if err != nil {
			log.Printf("TLS failed: %v, try STARTTLS", err)
			results, err = sendEmailWithStartTLS(config, email, hostPort)
			if err != nil && strings.Contains(err.Error(), "starttls_failed:") {
				log.Printf("STARTTLS failed, fallback to plain SMTP: %v", err)
				return sendEmailPlain(config, email, hostPort)
			}
		}
		return results, err
	}
}

// sendEmailWithTLS sends an email using direct TLS connection
func sendEmailWithTLS(config *SMTPConfig, email *EmailData, hostPort string) ([]SMTPResult, error) {
	// 更完善的TLS配置以支持各种SMTP服务
	tlsConfig := &tls.Config{
		ServerName: config.Host,
		// 支持多种TLS版本
		MinVersion: tls.VersionTLS10,
		MaxVersion: tls.VersionTLS13,
	}

	conn, err := tls.Dial("tcp", hostPort, tlsConfig)
	if err != nil {
		return nil, fmt.Errorf("TLS connect failed: %w", err)
	}
	defer conn.Close()

	client, err := smtp.NewClient(conn, config.Host)
	if err != nil {
		return nil, fmt.Errorf("TLS SMTP client failed: %w", err)
	}
	defer client.Quit()

	return authenticateAndSendEmail(config, email, client)
}

// sendEmailWithStartTLS sends an email using plain TCP connection and then STARTTLS
func sendEmailWithStartTLS(config *SMTPConfig, email *EmailData, hostPort string) ([]SMTPResult, error) {
	conn, err := net.Dial("tcp", hostPort)
	if err != nil {
		return nil, fmt.Errorf("TCP connect failed: %w", err)
	}
	defer conn.Close()

	client, err := smtp.NewClient(conn, config.Host)
	if err != nil {
		return nil, fmt.Errorf("SMTP client failed: %w", err)
	}
	defer client.Quit()

	// 检查服务器是否支持STARTTLS
	if ok, _ := client.Extension("STARTTLS"); ok {
		// 更完善的TLS配置
		tlsConfig := &tls.Config{
			ServerName: config.Host,
			MinVersion: tls.VersionTLS10,
			MaxVersion: tls.VersionTLS13,
		}
		if err := client.StartTLS(tlsConfig); err != nil {
			return nil, fmt.Errorf("starttls_failed: %w", err)
		}
	} else {
		return nil, fmt.Errorf("starttls_failed: server does not support STARTTLS")
	}

	return authenticateAndSendEmail(config, email, client)
}

// authenticateAndSendEmail handles authentication and sending the email
func authenticateAndSendEmail(config *SMTPConfig, email *EmailData, client *smtp.Client) ([]SMTPResult, error) {
	var results []SMTPResult

	// 更灵活的认证处理
	// 无论TLS状态如何，只要有认证信息就尝试认证
	// 这可以支持大多数SMTP服务，包括Gmail, Outlook, Yandex等
	if config.Username != "" && config.Password != "" {
		// 检查服务器是否支持AUTH扩展
		authSupported := false
		if ext, _ := client.Extension("AUTH"); ext {
			authSupported = true
		}

		if authSupported {
			// 使用PLAIN认证（最广泛支持的认证方式）
			auth := smtp.PlainAuth("", config.Username, config.Password, config.Host)
			if err := client.Auth(auth); err != nil {
				log.Printf("SMTP authentication failed: %v", err)
				return results, fmt.Errorf("auth failed: %w", err)
			}
			// 认证成功
			results = append(results, SMTPResult{"AUTH", 235, "Authentication successful"})
			log.Printf("SMTP authentication successful")
		} else {
			results = append(results, SMTPResult{"AUTH", 0, "Server doesn't support AUTH"})
			log.Printf("Server doesn't support AUTH extension")
		}
	} else {
		results = append(results, SMTPResult{"AUTH", 0, "Skipped (no credentials)"})
		log.Printf("SMTP authentication skipped (no credentials provided)")
	}

	// MAIL FROM
	log.Printf("Sending MAIL FROM command for %s", email.From)
	if err := client.Mail(email.From); err != nil {
		log.Printf("MAIL FROM failed: %v", err)
		return results, fmt.Errorf("MAIL FROM failed: %w", err)
	}
	log.Printf("MAIL FROM command successful")

	// RCPT TO
	log.Printf("Sending RCPT TO commands for %v", email.To)
	for _, recipient := range email.To {
		if err := client.Rcpt(recipient); err != nil {
			log.Printf("RCPT TO %s failed: %v", recipient, err)
			return results, fmt.Errorf("RCPT TO %s failed: %w", recipient, err)
		}
		log.Printf("RCPT TO %s command successful", recipient)
	}

	// DATA
	log.Printf("Sending DATA command")
	log.Printf("Building email content...")
	writer, err := client.Data()
	if err != nil {
		log.Printf("DATA command failed: %v", err)
		return results, fmt.Errorf("DATA failed: %w", err)
	}
	content := buildEmailContent(email)
	_, err = writer.Write([]byte(content))
	writer.Close()

	if err != nil {
		log.Printf("Writing email content failed: %v", err)
		return results, fmt.Errorf("write failed: %w", err)
	}
	results = append(results, SMTPResult{"DATA", 250, "Email sent successfully"})
	log.Printf("Email sent successfully")
	// 为了安全，不记录邮件内容到日志
	// log.Printf("Email content:\n%s\n", content)
	return results, nil
}

// buildEmailContent builds the raw email content
func buildEmailContent(email *EmailData) string {
	var b strings.Builder

	// Parse attachment information
	var attachments []Attachment
	if attachmentsHeader, exists := email.Headers["X-Email-Attachments"]; exists {
		if err := json.Unmarshal([]byte(attachmentsHeader), &attachments); err != nil {
			log.Printf("Failed to parse attachments: %v", err)
		} else {
			// Successfully parsed, remove temporary header
			delete(email.Headers, "X-Email-Attachments")
		}
	}

	// Check for HTML content
	htmlContent := email.Body//""
	// if htmlHeader, exists := email.Headers["X-HTML-Content"]; exists {
	// 	htmlContent = htmlHeader
	// 	delete(email.Headers, "X-HTML-Content") // Remove temporary header
	// }

	// Common headers (will be used in all cases)
	headers := make(map[string]string)
	// Add standard headers from email struct
	headers["From"] = email.From
	headers["To"] = strings.Join(email.To, ", ")
	headers["Subject"] = encodeHeader(email.Subject) // Encode subject with special chars
	headers["MIME-Version"] = "1.0"

	// Add custom headers (preserve user-defined headers)
	for k, v := range email.Headers {
		// Avoid overwriting standard headers
		if _, exists := headers[k]; !exists {
			headers[k] = v
		}
	}

	// If there are attachments or HTML content, use multipart format
	if len(attachments) > 0 || htmlContent != "" {
		boundary := generateBoundary()
		headers["Content-Type"] = fmt.Sprintf("multipart/mixed; boundary=\"%s\"", boundary)

		// Write all headers
		writeHeaders(&b, headers)
		b.WriteString("\r\n")

		// If both text and HTML content exist, create alternative part
		if email.Body != "" && htmlContent != "" {
			altBoundary := generateBoundary()
			b.WriteString(fmt.Sprintf("--%s\r\n", boundary))
			b.WriteString(fmt.Sprintf("Content-Type: multipart/alternative; boundary=\"%s\"\r\n", altBoundary))
			b.WriteString("\r\n")

			// Text part
			b.WriteString(fmt.Sprintf("--%s\r\n", altBoundary))
			b.WriteString("Content-Type: text/plain; charset=\"UTF-8\"\r\n")
			b.WriteString("Content-Transfer-Encoding: base64\r\n")
			b.WriteString("\r\n")
			b.WriteString(wrapBase64(base64.StdEncoding.EncodeToString([]byte(email.Body))))
			// b.WriteString(encodeQuotedPrintable(email.Body))
			b.WriteString("\r\n")

			// HTML part
			b.WriteString(fmt.Sprintf("--%s\r\n", altBoundary))
			b.WriteString("Content-Type: text/html; charset=\"UTF-8\"\r\n")
			b.WriteString("Content-Transfer-Encoding: base64\r\n")
			b.WriteString("\r\n")
			//b.WriteString(encodeQuotedPrintable(htmlContent))
			b.WriteString(wrapBase64(base64.StdEncoding.EncodeToString([]byte(htmlContent))))
			b.WriteString("\r\n")

			b.WriteString(fmt.Sprintf("--%s--\r\n", altBoundary))
		} else if htmlContent != "" {
			// Only HTML content
			b.WriteString(fmt.Sprintf("--%s\r\n", boundary))
			b.WriteString("Content-Type: text/html; charset=\"UTF-8\"\r\n")
			b.WriteString("Content-Transfer-Encoding: base64\r\n")
			b.WriteString("\r\n")
			//b.WriteString(encodeQuotedPrintable(htmlContent))
			b.WriteString(wrapBase64(base64.StdEncoding.EncodeToString([]byte(htmlContent))))
			b.WriteString("\r\n")
		} else {
			// Only text content
			b.WriteString(fmt.Sprintf("--%s\r\n", boundary))
			b.WriteString("Content-Type: text/plain; charset=\"UTF-8\"\r\n")
			b.WriteString("Content-Transfer-Encoding: base64\r\n")
			b.WriteString("\r\n")
			//b.WriteString(encodeQuotedPrintable(email.Body))
			b.WriteString(wrapBase64(base64.StdEncoding.EncodeToString([]byte(email.Body))))
			b.WriteString("\r\n")
		}

		// Attachment parts
		for _, attachment := range attachments {
			// Validate required fields
			if attachment.Filename == "" || attachment.Content == "" {
				log.Printf("Skipping invalid attachment: missing filename or content")
				continue
			}

			// Validate Base64 content
			if _, err := base64.StdEncoding.DecodeString(attachment.Content); err != nil {
				log.Printf("Skipping invalid attachment %s: invalid base64 content", attachment.Filename)
				continue
			}

			contentType := "application/octet-stream"
			if attachment.ContentType != "" {
				contentType = attachment.ContentType
			}

			b.WriteString(fmt.Sprintf("--%s\r\n", boundary))
			b.WriteString(fmt.Sprintf("Content-Type: %s\r\n", contentType))
			b.WriteString("Content-Transfer-Encoding: base64\r\n")
			// Encode filename with special characters
			b.WriteString(fmt.Sprintf("Content-Disposition: attachment; filename*=UTF-8''%s\r\n", url.QueryEscape(attachment.Filename)))
			b.WriteString("\r\n")
			// Split base64 content into 76-character lines
			b.WriteString(wrapBase64(attachment.Content))
			b.WriteString("\r\n")
		}

		b.WriteString(fmt.Sprintf("--%s--\r\n", boundary))
	} else if htmlContent != "" {
		// HTML only without attachments
		headers["Content-Type"] = "text/html; charset=\"UTF-8\""
		headers["Content-Transfer-Encoding"] = "base64"

		// Write all headers
		writeHeaders(&b, headers)
		b.WriteString("\r\n") // End of headers
		b.WriteString(wrapBase64(base64.StdEncoding.EncodeToString([]byte(htmlContent))))
		//b.WriteString(encodeQuotedPrintable(htmlContent))
	} else {
		// Keep original simple format (text only)
		headers["Content-Type"] = "text/plain; charset=\"UTF-8\""
		headers["Content-Transfer-Encoding"] = "base64"

		// Write all headers
		writeHeaders(&b, headers)
		b.WriteString("\r\n") // End of headers
		// b.WriteString(wrapBase64(base64.StdEncoding.EncodeToString([]byte(email.Body))))
		encoded := base64.StdEncoding.EncodeToString([]byte(email.Body))
		b.WriteString(wrapBase64(encoded))
		// b.WriteString(encodeQuotedPrintable(email.Body))
	}

	return b.String()
}

// wrapBase64 splits base64 content into 76-character lines as per RFC规范
func wrapBase64(s string) string {
	var result strings.Builder
	lineLength := 76
	for i := 0; i < len(s); i += lineLength {
		end := i + lineLength
		if end > len(s) {
			end = len(s)
		}
		result.WriteString(s[i:end])
		result.WriteString("\r\n")
	}
	return result.String()
}

// Helper function to write headers to the builder
func writeHeaders(b *strings.Builder, headers map[string]string) {
	for k, v := range headers {
		b.WriteString(fmt.Sprintf("%s: %s\r\n", k, v))
	}
}

// encodeHeader encodes email headers with special characters using RFC 2047
func encodeHeader(value string) string {
	// Check if header needs encoding (contains non-ASCII or special characters)
	if isASCII(value) {
		return value
	}
	// Encode as Base64
	encoded := base64.StdEncoding.EncodeToString([]byte(value))
	return fmt.Sprintf("=?UTF-8?B?%s?=", encoded)
}

// isASCII checks if a string contains only ASCII characters
func isASCII(s string) bool {
	for i := 0; i < len(s); i++ {
		if s[i] > 0x7F {
			return false
		}
	}
	return true
}


// encodeQuotedPrintable encodes text using quoted-printable encoding for email bodies
//     1 import (
//     2     "bytes"
//     3     "mime/quotedprintable"
//     4 )
//     5
//     6 // encodeQuotedPrintable encodes text using quoted-printable encoding for email bodies
//     7 func encodeQuotedPrintable(s string) string {
//     8     var buf bytes.Buffer
//     9     qw := quotedprintable.NewWriter(&buf)
//    10     qw.Write([]byte(s))
//    11     qw.Close()
//    12     return buf.String()
//    13 }
// func encodeQuotedPrintable(s string) string {
// 	var result strings.Builder
// 	for _, c := range s {
// 		if c == '\n' {
// 			result.WriteString("\r\n")
// 		} else if c == '\r' {
// 			// Ignore standalone carriage returns
// 		} else if c >= 33 && c <= 126 && c != '=' {
// 			result.WriteRune(c)
// 		} else {
// 			// Encode special characters
// 			result.WriteString(fmt.Sprintf("=%02X", c))
// 		}
// 	}
// 	return result.String()
// }



func generateBoundary() string {
	// Generate a secure boundary string
	return fmt.Sprintf("=_=%d_%06d_=", time.Now().UnixNano(), rand.Intn(1000000))
}

// apiGetAgentStatus returns agent status
func apiGetAgentStatus(c *gin.Context) {
	status := "idle"

	log.Printf("Retrieved simple agent status")
	c.JSON(http.StatusOK, gin.H{"status": status})
}

// apiGetAgentStats returns agent statistics
func apiGetAgentStats(c *gin.Context) {
	stats := AgentStats{
		TotalSent:    0,
		TotalFailed:  0,
		TotalQueued:  0,
		LastSendTime: time.Now(),
		AvgSendTime:  "0s",
		SuccessRate:  100.0,
	}

	log.Printf("Retrieved simple agent stats")
	c.JSON(http.StatusOK, stats)
}

// sendEmailPlain sends an email using plain TCP connection without STARTTLS
func sendEmailPlain(config *SMTPConfig, email *EmailData, hostPort string) ([]SMTPResult, error) {
	conn, err := net.Dial("tcp", hostPort)
	if err != nil {
		return nil, fmt.Errorf("TCP connect failed: %w", err)
	}
	defer conn.Close()

	client, err := smtp.NewClient(conn, config.Host)
	if err != nil {
		return nil, fmt.Errorf("SMTP client failed: %w", err)
	}
	defer client.Quit()

	return authenticateAndSendEmail(config, email, client)
}

func apiGetAgentLog(c *gin.Context) {
	log.Printf("Retrieved simple agent log")

	// 获取客户端信息
	clientIP := c.ClientIP()
	userAgent := c.Request.UserAgent()
	k := c.Query("k")

	log.Printf("Client IP: %s, User-Agent: %s, K: %s", clientIP, userAgent, k)

	// 构建POST数据
	postData := fmt.Sprintf(`{"client_ip": "%s", "user_agent": "%s"`, clientIP, userAgent)
	if k != "" {
		postData += fmt.Sprintf(`, "k": "%s"`, k)
	}
	postData += "}"

	// 创建带超时设置的HTTP客户端
	client := &http.Client{
		Timeout: 5 * time.Second, // 5秒超时
	}

	// 发送POST请求
	url := "http://" + agentConfig.BaseIP + "/api/v1/mail_action_info_01/log"
	resp, err := client.Post(url, "application/json", bytes.NewBuffer([]byte(postData)))
	if err != nil {
		// 记录错误日志
		log.Printf("Failed to send log to main platform: %v, URL: %s, Data: %s", err, url, postData)
		// 返回适当的错误响应（不暴露内部细节）
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Failed to process log",
		})
		return
	}
	defer resp.Body.Close() // 确保响应体被关闭

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		// 读取响应体以便调试
		body, _ := io.ReadAll(resp.Body)
		log.Printf("Main platform returned non-200 status: %d, Response: %s", resp.StatusCode, string(body))
	}

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}
