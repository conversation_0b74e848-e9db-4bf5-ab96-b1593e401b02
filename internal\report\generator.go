package report

import (
	"fmt"
	"html/template"
	"os"
)

// ReportGenerator generates reports
type ReportGenerator struct {
	TemplatePath string
	OutputPath   string
}

// NewReportGenerator creates a new report generator
func NewReportGenerator(templatePath, outputPath string) *ReportGenerator {
	return &ReportGenerator{
		TemplatePath: templatePath,
		OutputPath:   outputPath,
	}
}

// GenerateHTML generates an HTML report
func (r *ReportGenerator) GenerateHTML(data interface{}) error {
	// Parse template
	tmpl, err := template.ParseFiles(r.TemplatePath)
	if err != nil {
		return err
	}

	// Create output file
	file, err := os.Create(r.OutputPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Execute template
	err = tmpl.Execute(file, data)
	if err != nil {
		return err
	}

	fmt.Printf("HTML report generated: %s\n", r.OutputPath)
	return nil
}