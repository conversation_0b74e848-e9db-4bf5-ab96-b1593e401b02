package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/glebarez/sqlite"
	"gorm.io/gorm"

	"aiphish/internal/auth"
	"aiphish/internal/models"
	"aiphish/internal/plugin"
	"aiphish/internal/scheduler"
)

// Task represents a phishing task
type Task struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      string `json:"status"` // pending, running, completed, failed
}

// Plugin represents a vulnerability plugin
type Plugin struct {
	ID      uint   `gorm:"primaryKey" json:"id"`
	Name    string `json:"name"`
	Version string `json:"version"`
	Target  string `json:"target"`
}

var db *gorm.DB
var pluginEngine *plugin.PluginEngine
var taskScheduler *scheduler.TaskScheduler

func initDB() {
	var err error
	db, err = gorm.Open(sqlite.Open("aiphish.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("failed to connect database:", err)
	}

	// Migrate the schema
	db.AutoMigrate(&models.User{}, &Task{}, &Plugin{})
	
	// Initialize plugin engine
	pluginEngine = plugin.NewPluginEngine("./plugins")
	
	// Initialize task scheduler
	taskScheduler = scheduler.NewTaskScheduler()
}

func main() {
	initDB()

	r := gin.Default()

	// Serve static files
	r.Static("/static", "./web/static")
	r.LoadHTMLGlob("./web/templates/*")

	// Routes
	r.GET("/", indexHandler)
	r.GET("/tasks", getTasks)
	r.POST("/tasks", createTask)
	r.GET("/plugins", getPlugins)
	r.POST("/plugins", createPlugin)
	r.GET("/email-test", getEmailTestPage)
	r.GET("/plugin-management", getPluginManagementPage)

	// Auth routes
	authGroup := r.Group("/auth")
	{
		authGroup.POST("/register", register)
		authGroup.POST("/login", login)
	}

	// API routes
	api := r.Group("/api/v1")
	{
		api.POST("/register", apiRegister)
		api.POST("/login", apiLogin)
		api.GET("/tasks", apiGetTasks)
		api.POST("/tasks", apiCreateTask)
		api.GET("/plugins", apiGetPlugins)
		api.POST("/plugins", apiCreatePlugin)
		api.GET("/plugins/list", apiListPlugins)
		api.GET("/plugins/:name/info", apiGetPluginInfo)
		api.POST("/plugins/:name/run", apiRunPlugin)
		api.POST("/send-test-email", apiSendTestEmail)
		api.POST("/scheduler/tasks", apiAddTask)
		api.POST("/scheduler/tasks/:id/run", apiRunTask)
		api.POST("/scheduler/tasks/:id/schedule", apiScheduleTask)
		api.POST("/scheduler/tasks/:id/cancel", apiCancelScheduledTask)
		api.GET("/scheduler/tasks/:id/status", apiGetTaskStatus)
		api.GET("/scheduler/tasks", apiListTasks)
	}

	fmt.Println("Server starting on :8080")
	r.Run(":8080")
}

func indexHandler(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", nil)
}

// getEmailTestPage handles the email test page request
func getEmailTestPage(c *gin.Context) {
	// In a real application, you would check if the user is logged in here
	// For now, we'll just serve the page
	c.HTML(http.StatusOK, "email_test.html", nil)
}

// getPluginManagementPage handles the plugin management page request
func getPluginManagementPage(c *gin.Context) {
	// In a real application, you would check if the user is logged in here
	// For now, we'll just serve the page
	c.HTML(http.StatusOK, "plugin_management.html", nil)
}

// RegisterRequest represents the request structure for user registration
type RegisterRequest struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// LoginRequest represents the request structure for user login
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// register handles user registration
func register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if user already exists
	var existingUser models.User
	if err := db.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User already exists"})
		return
	}

	// Create new user
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password,
	}

	if err := db.Create(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User registered successfully"})
}

// login handles user login
func login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find user by username
	var user models.User
	if err := db.Where("username = ?", req.Username).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Check password
	if !user.ComparePassword(req.Password) {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Generate JWT token
	token, err := auth.GenerateToken(user.Username, user.Email)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Login successful",
		"token":   token,
	})
}

// apiRegister is the API endpoint for user registration
func apiRegister(c *gin.Context) {
	register(c)
}

// apiLogin is the API endpoint for user login
func apiLogin(c *gin.Context) {
	login(c)
}

func getTasks(c *gin.Context) {
	var tasks []Task
	db.Find(&tasks)
	c.HTML(http.StatusOK, "tasks.html", gin.H{"tasks": tasks})
}

func createTask(c *gin.Context) {
	var task Task
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	db.Create(&task)
	c.JSON(http.StatusOK, task)
}

func getPlugins(c *gin.Context) {
	var plugins []Plugin
	db.Find(&plugins)
	c.HTML(http.StatusOK, "plugins.html", gin.H{"plugins": plugins})
}

func createPlugin(c *gin.Context) {
	var plugin Plugin
	if err := c.ShouldBindJSON(&plugin); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	db.Create(&plugin)
	c.JSON(http.StatusOK, plugin)
}

// apiGetTasks gets all tasks
func apiGetTasks(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	var tasks []Task
	db.Find(&tasks)
	c.JSON(http.StatusOK, tasks)
}

// apiCreateTask creates a new task
func apiCreateTask(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	var task Task
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	db.Create(&task)
	c.JSON(http.StatusOK, task)
}

// apiGetPlugins gets all plugins
func apiGetPlugins(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	var plugins []Plugin
	db.Find(&plugins)
	c.JSON(http.StatusOK, plugins)
}

// apiCreatePlugin creates a new plugin
func apiCreatePlugin(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	var plugin Plugin
	if err := c.ShouldBindJSON(&plugin); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	db.Create(&plugin)
	c.JSON(http.StatusOK, plugin)
}

// apiSendTestEmail sends a test email
func apiSendTestEmail(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// For testing purposes, we'll use hardcoded SMTP settings
	// In a real application, these would come from a configuration file or database
	// emailAgent := agent.NewEmailAgent("smtp.example.com", "587", "<EMAIL>", "password")

	// // Send test email
	// err = emailAgent.SendMail("<EMAIL>", "<EMAIL>", "Test Subject", "This is a test email body.")
	// if err != nil {
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send email: " + err.Error()})
	// 	return
	// }

	c.JSON(http.StatusOK, gin.H{"message": "Test email functionality is not fully implemented yet"})
}

// apiListPlugins lists all available plugins
func apiListPlugins(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// List plugins
	plugins, err := pluginEngine.ListPlugins()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list plugins: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"plugins": plugins})
}

// apiGetPluginInfo gets information about a specific plugin
func apiGetPluginInfo(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get plugin name from URL parameter
	pluginName := c.Param("name")
	if pluginName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plugin name is required"})
		return
	}

	// Get plugin info
	info, err := pluginEngine.GetPluginInfo(pluginName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get plugin info: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, info)
}

// apiRunPlugin runs a specific plugin
func apiRunPlugin(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get plugin name from URL parameter
	pluginName := c.Param("name")
	if pluginName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plugin name is required"})
		return
	}

	// Parse request body
	var request plugin.PluginRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set plugin name in request
	request.Name = pluginName

	// Run plugin
	response, err := pluginEngine.RunPlugin(request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to run plugin: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// apiSendTestEmail sends a test email
func apiSendTestEmail(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// For testing purposes, we'll use hardcoded SMTP settings
	// In a real application, these would come from a configuration file or database
	// emailAgent := agent.NewEmailAgent("smtp.example.com", "587", "<EMAIL>", "password")

	// // Send test email
	// err = emailAgent.SendMail("<EMAIL>", "<EMAIL>", "Test Subject", "This is a test email body.")
	// if err != nil {
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send email: " + err.Error()})
	// 	return
	// }

	c.JSON(http.StatusOK, gin.H{"message": "Test email functionality is not fully implemented yet"})
}

// apiAddTask adds a new task to the scheduler
func apiAddTask(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Parse request body
	var request struct {
		ID   string `json:"id" binding:"required"`
		Name string `json:"name" binding:"required"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Add task to scheduler
	// For demonstration, we'll add a simple task that logs a message
	err = taskScheduler.AddTask(request.ID, request.Name, func() error {
		log.Printf("Executing scheduled task: %s", request.Name)
		// Simulate some work
		time.Sleep(2 * time.Second)
		log.Printf("Completed scheduled task: %s", request.Name)
		return nil
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add task: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Task added successfully"})
}

// apiRunTask runs a specific task
func apiRunTask(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get task ID from URL parameter
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// Run task
	err = taskScheduler.RunTask(taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to run task: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Task executed successfully"})
}

// apiScheduleTask schedules a task to run at a specific interval
func apiScheduleTask(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get task ID from URL parameter
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// Parse request body
	var request struct {
		Interval int64 `json:"interval" binding:"required"` // Interval in seconds
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Schedule task
	err = taskScheduler.ScheduleTask(taskID, time.Duration(request.Interval)*time.Second)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to schedule task: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Task scheduled successfully"})
}

// apiCancelScheduledTask cancels a scheduled task
func apiCancelScheduledTask(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get task ID from URL parameter
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// Cancel scheduled task
	err = taskScheduler.CancelScheduledTask(taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel scheduled task: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Scheduled task cancelled successfully"})
}

// apiGetTaskStatus gets the status of a task
func apiGetTaskStatus(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get task ID from URL parameter
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// Get task status
	task, err := taskScheduler.GetTaskStatus(taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get task status: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, task)
}

// apiListTasks lists all tasks
func apiListTasks(c *gin.Context) {
	// Check JWT token
	_, err := auth.ParseToken(c.GetHeader("Authorization"))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// List tasks
	tasks := taskScheduler.ListTasks()

	c.JSON(http.StatusOK, tasks)
}