package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/glebarez/sqlite"
	"gorm.io/gorm"

	"aiphish/internal/agent"
	"aiphish/internal/auth"
	"aiphish/internal/logger"
	"aiphish/internal/models"
	"aiphish/internal/plugin"
	"aiphish/internal/report"
	"aiphish/internal/scheduler"
)

// PluginConfig represents a plugin with its configuration
type PluginConfig struct {
	Name   string                 `json:"name"`
	Params map[string]interface{} `json:"params"`
}

// Global variables for dependencies
var (
	db                    *gorm.DB
	appLogger             *logger.Logger
	agentManager          *agent.AgentManager
	pluginEngine          *plugin.PluginEngine
	phishingTaskScheduler *scheduler.PhishingTaskScheduler // Keep only the phishing task scheduler
	reportGenerator       *report.ReportGenerator
)

func main() {
	// Initialize logger
	appLogger = logger.NewLogger()
	appLogger.Info("Starting Phish X platform")

	// Initialize database
	dbPath := "aiphish.db"
	var err error
	db, err = gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	appLogger.Info("Connected to database")

	// Migrate database models
	err = db.AutoMigrate(&models.User{}, &models.Task{}, &models.Plugin{}, &models.Agent{})
	if err != nil {
		log.Fatal("Failed to migrate database models:", err)
	}
	appLogger.Info("Database migration completed")

	// Initialize components
	initializeComponents()

	// Create router
	router := gin.Default()

	// Serve static files
	router.Static("/static", "./web/static")
	router.LoadHTMLGlob("./web/templates/*")

	// API routes
	api := router.Group("/api/v1")
	{
		// Authentication routes
		api.POST("/register", apiRegister)
		api.POST("/login", apiLogin)

		// Log route
		api.POST("/mail_action_info_01/log", apiLogEmailOpen)
		// Protected routes
		protected := api.Group("/")
		protected.Use(authMiddleware())
		{
			// Agent routes
			agents := protected.Group("/agents")
			{
				agents.POST("", apiRegisterAgent)
				agents.GET("", apiListAgents)
				agents.GET("/:id", apiGetAgent)
				agents.DELETE("/:id", apiDeleteAgent)
			}

			// Task routes
			tasks := protected.Group("/tasks")
			{
				tasks.GET("", apiListTasks)
				tasks.GET("/:id", apiGetTask)
				tasks.POST("", apiCreateTask)
				tasks.POST("/:id/run", apiRunTask)
			}

			// Plugin routes
			plugins := protected.Group("/plugins")
			{
				plugins.GET("/list", apiListPlugins)
				plugins.GET("/:name", apiGetPlugin)
				plugins.GET("/:name/info", apiGetPluginInfo)
				plugins.POST("/:name/run", apiRunPlugin)
			}

			// Scheduler routes (simplified to only phishing tasks)
			schedulerGroup := protected.Group("/scheduler")
			{
				// For now, we'll keep the existing scheduler routes, but they will only work with phishing tasks
				// In a more thorough simplification, we might rename these or create new specific routes
				schedulerGroup.POST("/tasks", apiAddScheduledTask) // This will need to be adapted
				schedulerGroup.POST("/tasks/:id/run", apiRunScheduledTask)
				schedulerGroup.POST("/tasks/:id/schedule", apiScheduleTask)
				schedulerGroup.GET("/tasks/:id/status", apiGetScheduledTaskStatus)
				schedulerGroup.GET("/tasks", apiListScheduledTasks)
			}

			// Dashboard route
			protected.GET("/dashboard/data", apiGetDashboardData)
		}
	}

	// Web routes
	router.GET("/", webIndex)
	router.GET("/login", webLogin)
	router.GET("/register", webRegister)

	// Protected web routes
	protectedWeb := router.Group("/")
	protectedWeb.Use(authMiddleware())
	{
		protectedWeb.GET("/dashboard", webDashboard)
		protectedWeb.GET("/agent-management", webAgentManagement)
		protectedWeb.GET("/plugin-management", webPluginManagement)
		protectedWeb.GET("/email-test", webEmailTest)
		protectedWeb.GET("/scheduler", webScheduler)
		protectedWeb.GET("/tasks", webTasks)
	}

	// Start server
	port := "8089"
	appLogger.Infof("Starting platform server on port %s", port)
	err = router.Run(":" + port)
	if err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

// initializeComponents initializes all application components
func initializeComponents() {
	// Initialize agent manager
	agentManager = agent.NewAgentManager()
	appLogger.Info("Agent manager initialized")

	// Initialize plugin engine
	pluginPath, _ := filepath.Abs("./plugins")
	pluginEngine = plugin.NewPluginEngine(pluginPath)
	appLogger.Info("Plugin engine initialized")

	// Initialize phishing task scheduler (replaces general task scheduler)
	phishingTaskScheduler = scheduler.NewPhishingTaskScheduler(pluginEngine, agentManager, db)
	appLogger.Info("Phishing task scheduler initialized")

	// Load existing agents from database
	loadAgentsFromDatabase()

	// Load existing tasks from database
	loadTasksFromDatabase()

	// Initialize report generator
	reportGenerator = report.NewReportGenerator("./web/templates/report.html", "./web/static/reports/report.html")
	appLogger.Info("Report generator initialized")
}

// loadTasksFromDatabase loads existing tasks from the database into the scheduler
func loadTasksFromDatabase() {
	var tasks []models.Task
	result := db.Find(&tasks)
	if result.Error != nil {
		appLogger.Error("Failed to load tasks from database: " + result.Error.Error())
		return
	}

	for _, task := range tasks {
		var phishingTask *scheduler.PhishingTask

		// Try to deserialize the complete task data from JSON
		if task.TaskData != "" {
			err := json.Unmarshal([]byte(task.TaskData), &phishingTask)
			if err != nil {
				appLogger.Error(fmt.Sprintf("Failed to deserialize task data for task %d: %v", task.ID, err))
				// Create a basic phishing task with information from the database
				phishingTask = &scheduler.PhishingTask{
					ID:          task.ID,
					Name:        task.Name,
					Description: task.Description,
					Status:      scheduler.TaskStatus(task.Status),
					Created:     time.Now(), // We don't have the original creation time, so use current time
				}
			} else {
				if phishingTask.Status != scheduler.TaskStatusCompleted && phishingTask.Status != scheduler.TaskStatusFailed {
					phishingTask.Status = scheduler.TaskStatus(task.Status)
				}
			}
		} else {
			// Create a basic phishing task with information from the database
			phishingTask = &scheduler.PhishingTask{
				ID:          task.ID,
				Name:        task.Name,
				Description: task.Description,
				Status:      scheduler.TaskStatus(task.Status),
				Created:     time.Now(), // We don't have the original creation time, so use current time
			}
		}

		// Add task to scheduler (check for nil before adding)
		if phishingTask != nil {
			phishingTaskScheduler.AddTaskDirectly(phishingTask)
		} else {
			appLogger.Error(fmt.Sprintf("Skipping nil task %d", task.ID))
		}
	}

	appLogger.Infof("Loaded %d tasks from database", len(tasks))
}

// loadAgentsFromDatabase loads existing agents from the database into the agent manager
func loadAgentsFromDatabase() {
	var agents []models.Agent
	result := db.Find(&agents)
	if result.Error != nil {
		appLogger.Error("Failed to load agents from database: " + result.Error.Error())
		return
	}

	for _, agentModel := range agents {
		// Register agent with agent manager
		err := agentManager.RegisterAgent(agentModel.ID, agentModel.APIEndpoint)
		if err != nil {
			appLogger.Error(fmt.Sprintf("Failed to register agent %s: %v", agentModel.ID, err))
		} else {
			appLogger.Info(fmt.Sprintf("Registered agent %s from database", agentModel.ID))
		}
	}

	appLogger.Infof("Loaded %d agents from database", len(agents))
}

// authMiddleware is a Gin middleware for JWT authentication
func authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// First, try to get token from Authorization header (API requests)
		authHeader := c.GetHeader("Authorization")
		tokenString := extractToken(authHeader)

		// If not found in header, try to get from localStorage (web requests)
		// For web requests, we check if it's an HTML request
		isWebPageRequest := strings.Contains(c.GetHeader("Accept"), "text/html")

		if tokenString == "" && isWebPageRequest {
			// For web page requests, try to get token from localStorage via JavaScript
			// We'll let the frontend handle this by serving the page and letting JavaScript check auth
			c.Next()
			return
		}

		if tokenString == "" {
			appLogger.Info("Missing or invalid Authorization header")
			// For web requests, redirect to login page with return URL
			if isWebPageRequest {
				c.Redirect(http.StatusFound, "/login?redirect="+url.QueryEscape(c.Request.URL.Path))
				c.Abort()
				return
			}
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		_, err := auth.ParseToken(tokenString)
		if err != nil {
			appLogger.Info("Unauthorized access")
			// For web requests, redirect to login page with return URL
			if isWebPageRequest {
				c.Redirect(http.StatusFound, "/login?redirect="+url.QueryEscape(c.Request.URL.Path))
				c.Abort()
				return
			}
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// extractToken extracts the token from the Authorization header
func extractToken(authHeader string) string {
	if authHeader == "" {
		return ""
	}

	// Check if the header starts with "Bearer "
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		return authHeader[7:]
	}

	return ""
}

// API handler functions (placeholders for now)
func apiRegister(c *gin.Context) {
	// Parse request body
	var req struct {
		Username string `json:"username" binding:"required"`
		Email    string `json:"email" binding:"required"`
		Password string `json:"password" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		appLogger.Error("Failed to bind registration request: " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create user
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password, // Will be hashed by BeforeCreate hook
	}

	// Save user to database
	result := db.Create(&user)
	if result.Error != nil {
		appLogger.Error("Failed to create user: " + result.Error.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	appLogger.Info("User registered successfully: " + req.Username)
	c.JSON(http.StatusOK, gin.H{"message": "User registered successfully"})
}

func apiLogin(c *gin.Context) {
	// Parse request body
	var req struct {
		Username string `json:"username" binding:"required"`
		Password string `json:"password" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		appLogger.Error("Failed to bind login request: " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find user in database
	var user models.User
	result := db.Where("username = ?", req.Username).First(&user)
	if result.Error != nil {
		appLogger.Info("User not found: " + req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Compare password
	if !user.ComparePassword(req.Password) {
		appLogger.Info("Invalid password for user: " + req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Generate JWT token
	tokenString, err := auth.GenerateToken(user.Username, user.Email)
	if err != nil {
		appLogger.Error("Failed to generate token: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	appLogger.Info("User logged in successfully: " + req.Username)
	c.JSON(http.StatusOK, gin.H{
		"message": "Login successful",
		"token":   tokenString,
	})
}

// apiLogEmailOpen logs when an email is opened by the recipient
func apiLogEmailOpen(c *gin.Context) {
	// Parse request body
	var req struct {
		ClientIP  string `json:"client_ip"`
		UserAgent string `json:"user_agent"`
		K         string `json:"k"` // Tracking ID
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		appLogger.Error("Failed to bind email open log request: " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate required fields
	if req.K == "" {
		appLogger.Info("Tracking ID (k) is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tracking ID (k) is required"})
		return
	}

	// Log the email open event
	appLogger.Infof("Email opened - TrackingID: %s, ClientIP: %s, UserAgent: %s", req.K, req.ClientIP, req.UserAgent)

	// Search for tasks that contain this tracking ID
	var tasks []models.Task
	result := db.Find(&tasks)
	if result.Error != nil {
		appLogger.Error("Failed to fetch tasks: " + result.Error.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch tasks"})
		return
	}

	taskUpdated := false
	for _, task := range tasks {
		// Check if the task data contains the tracking ID
		if strings.Contains(task.TaskData, req.K) {
			// Parse existing task data as generic map to preserve all fields
			var taskData map[string]interface{}
			if err := json.Unmarshal([]byte(task.TaskData), &taskData); err != nil {
				appLogger.Error("Failed to parse task data: " + err.Error())
				continue
			}

			// Add email open tracking information
			emailOpenInfo := map[string]interface{}{
				"client_ip":   req.ClientIP,
				"user_agent":  req.UserAgent,
				"opened_at":   time.Now().Format(time.RFC3339),
				"tracking_id": req.K,
			}

			// Add to email_open_records array or create new one
			if existingRecords, exists := taskData["email_open_records"]; exists {
				if recordsArray, ok := existingRecords.([]interface{}); ok {
					taskData["email_open_records"] = append(recordsArray, emailOpenInfo)
				} else {
					taskData["email_open_records"] = []interface{}{emailOpenInfo}
				}
			} else {
				taskData["email_open_records"] = []interface{}{emailOpenInfo}
			}

			// Update task status to "opened"
			taskData["Status"] = "999"

			// Serialize back to JSON
			updatedTaskData, err := json.Marshal(taskData)
			if err != nil {
				appLogger.Error("Failed to serialize updated task data: " + err.Error())
				continue
			}

			// Save updated task data
			task.TaskData = string(updatedTaskData)
			task.Status = "999"
			result := db.Save(&task)
			if result.Error != nil {
				appLogger.Error("Failed to save task: " + result.Error.Error())
				continue
			}

			appLogger.Infof("Updated task %d with email open record", task.ID)
			taskUpdated = true
			break
		}
	}

	if !taskUpdated {
		appLogger.Infof("No task found for tracking ID: %s", req.K)
	}

	c.JSON(http.StatusOK, gin.H{"message": "Email open logged successfully"})
}

func apiRegisterAgent(c *gin.Context) {
	// Temporarily bypass authentication for agent registration
	// Check JWT token
	// authHeader := c.GetHeader("Authorization")
	// tokenString := extractToken(authHeader)
	// if tokenString == "" {
	// 	appLogger.Info("Missing or invalid Authorization header")
	// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
	// 	return
	// }

	// _, err := auth.ParseToken(tokenString)
	// if err != nil {
	// 	appLogger.Info("Unauthorized access to /api/v1/agents")
	// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
	// 	return
	// }

	// Parse request body
	var req struct {
		ID          string `json:"id" binding:"required"`
		APIEndpoint string `json:"api_endpoint" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		appLogger.Error("Failed to bind agent registration request: " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Register agent with agent manager
	appLogger.Infof("Registering agent with ID: %s and API endpoint: %s", req.ID, req.APIEndpoint)
	err := agentManager.RegisterAgent(req.ID, req.APIEndpoint)
	if err != nil {
		appLogger.Error("Failed to register agent: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to register agent"})
		return
	}

	// Save agent to database
	agentModel := models.Agent{
		ID:          req.ID,
		APIEndpoint: req.APIEndpoint,
		Status:      "unknown",
		CreatedAt:   time.Now().Unix(),
		LastSeen:    time.Now().Unix(),
	}
	result := db.Save(&agentModel)
	if result.Error != nil {
		appLogger.Error("Failed to save agent to database: " + result.Error.Error())
		// Note: We don't return an error here because the agent is already registered
		// in memory. The database persistence is just for persistence across restarts.
	}

	appLogger.Info("Registered agent successfully: " + req.ID)
	c.JSON(http.StatusOK, gin.H{"message": "Agent registered successfully"})
}

func apiListAgents(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/agents")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get list of agent IDs
	agentIDs := agentManager.ListAgents()

	appLogger.Info("Listed agents successfully")
	c.JSON(http.StatusOK, gin.H{"agents": agentIDs})
}

func apiGetAgent(c *gin.Context) {
	// Temporarily bypass authentication for agent retrieval
	// Check JWT token
	// authHeader := c.GetHeader("Authorization")
	// tokenString := extractToken(authHeader)
	// if tokenString == "" {
	// 	appLogger.Info("Missing or invalid Authorization header")
	// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
	// 	return
	// }

	// _, err := auth.ParseToken(tokenString)
	// if err != nil {
	// 	appLogger.Info("Unauthorized access to /api/v1/agents/:id")
	// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
	// 	return
	// }

	// Get agent ID from URL parameter
	agentID := c.Param("id")
	appLogger.Infof("Getting agent info for ID: %s", agentID)
	if agentID == "" {
		appLogger.Info("Agent ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Agent ID is required"})
		return
	}

	// Get agent from agent manager
	appLogger.Infof("Retrieving agent with ID: %s", agentID)
	agentClient, exists := agentManager.GetAgent(agentID)
	appLogger.Infof("Agent exists: %t", exists)
	if !exists {
		appLogger.Info("Agent not found: " + agentID)
		c.JSON(http.StatusNotFound, gin.H{"error": "Agent not found"})
		return
	}

	// Get agent info
	info, err := agentClient.GetInfo()
	if err != nil {
		appLogger.Error("Failed to get agent info: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get agent info"})
		return
	}

	// Create a response that includes both agent info and local agent data
	agentInfo := agentClient.GetAgentInfo()
	response := struct {
		ID          string    `json:"id"`
		Version     string    `json:"version"`
		Status      string    `json:"status"`
		APIEndpoint string    `json:"api_endpoint"`
		LastSeen    time.Time `json:"last_seen"`
	}{
		ID:          info.ID,
		Version:     info.Version,
		Status:      info.Status,
		APIEndpoint: agentInfo.APIEndpoint,
		LastSeen:    agentInfo.LastSeen,
	}

	appLogger.Info("Retrieved agent info successfully: " + agentID)
	c.JSON(http.StatusOK, response)
}

func apiDeleteAgent(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/agents/:id")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get agent ID from URL parameter
	agentID := c.Param("id")
	if agentID == "" {
		appLogger.Info("Agent ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Agent ID is required"})
		return
	}

	// Remove agent from agent manager
	agentManager.UnregisterAgent(agentID)

	// Remove agent from database
	var agentModel models.Agent
	result := db.Where("id = ?", agentID).Delete(&agentModel)
	if result.Error != nil {
		appLogger.Error("Failed to delete agent from database: " + result.Error.Error())
		// Note: We don't return an error here because the agent is already removed
		// from memory. The database persistence is just for persistence across restarts.
	}

	appLogger.Info("Deleted agent successfully: " + agentID)
	c.JSON(http.StatusOK, gin.H{"message": "Agent deleted successfully"})
}

func apiListTasks(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/tasks")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get list of tasks from database
	var tasks []models.Task
	db.Find(&tasks)

	appLogger.Info("Listed tasks successfully")
	c.JSON(http.StatusOK, tasks)
}

func apiGetTask(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/tasks/:id")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get task ID from URL parameter
	taskID := c.Param("id")
	if taskID == "" {
		appLogger.Info("Task ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// Get task from database
	var task models.Task
	db.Where("id = ?", taskID).First(&task)
	if task.ID == 0 {
		appLogger.Info("Task not found: " + taskID)
		c.JSON(http.StatusNotFound, gin.H{"error": "Task not found"})
		return
	}

	appLogger.Info("Retrieved task successfully: " + taskID)
	c.JSON(http.StatusOK, task)
}

func apiCreateTask(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/tasks")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Define request structure with initial email data and SMTP config
	var req struct {
		Name         string            `json:"name" binding:"required"`
		Description  string            `json:"description"`
		Plugins      []PluginConfig    `json:"plugins" binding:"required"`
		Targets      []string          `json:"Targets" binding:"required"`
		InitialEmail *plugin.EmailData `json:"initial_email"` // New field for initial email data
		SMTPConfig   *agent.SMTPConfig `json:"smtp_config"`   // New field for SMTP configuration
		TrackingID   string            `json:"tracking_id"`   // New field for tracking ID
	}

	// Bind JSON request body
	if err := c.ShouldBindJSON(&req); err != nil {
		appLogger.Error("Failed to bind task creation request: " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate a unique task ID
	taskID := generateTaskID()

	// Convert PluginConfig to string slice for compatibility with existing scheduler
	pluginNames := make([]string, len(req.Plugins))
	for i, pluginConfig := range req.Plugins {
		pluginNames[i] = pluginConfig.Name
	}

	// Add tracking ID to initial email if provided
	if req.InitialEmail != nil && req.TrackingID != "" {
		req.InitialEmail.TrackingID = req.TrackingID
	}

	// Create phishing task
	appLogger.Infof("Creating phishing task with targets: %v", req.Targets)
	phishingTask := &scheduler.PhishingTask{
		ID:           taskID,
		Name:         req.Name,
		Description:  req.Description,
		Plugins:      make([]scheduler.PluginConfig, len(req.Plugins)),
		Targets:      req.Targets,
		InitialEmail: req.InitialEmail,
		SMTPConfig:   req.SMTPConfig,
		TrackingID:   req.TrackingID,
		Status:       scheduler.TaskStatusPending,
		Created:      time.Now(),
	}

	// Convert PluginConfig to scheduler.PluginConfig
	for i, pluginConfig := range req.Plugins {
		phishingTask.Plugins[i] = scheduler.PluginConfig{
			Name:   pluginConfig.Name,
			Params: pluginConfig.Params,
		}
	}

	// Add task to scheduler
	phishingTaskScheduler.AddTaskDirectly(phishingTask)

	if err != nil {
		appLogger.Error("Failed to add phishing task: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add phishing task"})
		return
	}

	// Serialize the complete task data to JSON
	taskDataBytes, err := json.Marshal(phishingTask)
	if err != nil {
		appLogger.Error("Failed to serialize task data: " + err.Error())
		// We'll continue without storing the task data, as the task is already in memory
	}

	// Save task to database
	task := models.Task{
		ID:          taskID, // Use the generated task ID
		Name:        req.Name,
		Description: req.Description,
		Status:      "pending",
		TaskData:    string(taskDataBytes), // Store the complete task data as JSON
	}
	db.Create(&task)

	appLogger.Info(fmt.Sprintf("Created phishing task successfully: %d", taskID))
	c.JSON(http.StatusOK, gin.H{
		"message": "Task created successfully",
		"id":      taskID,
	})
}

func apiRunTask(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/tasks/:id/run")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get task ID from URL parameter
	taskID := c.Param("id")
	if taskID == "" {
		appLogger.Info("Task ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// Run phishing task
	err = phishingTaskScheduler.RunPhishingTask(taskID)
	if err != nil {
		appLogger.Error("Failed to run phishing task: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to run phishing task"})
		return
	}

	// Update task status in database
	// var task models.Task
	// db.Where("id = ?", taskID).First(&task)
	// if task.ID != 0 {
	// 	task.Status = "completed"
	// 	db.Save(&task)
	// }

	appLogger.Info("Ran phishing task successfully: " + taskID)
	c.JSON(http.StatusOK, gin.H{"message": "Task executed successfully"})
}

// apiListPlugins lists all available plugins
func apiListPlugins(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/plugins/list")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// List plugins
	plugins, err := pluginEngine.ListPlugins()
	if err != nil {
		appLogger.Error("Failed to list plugins: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list plugins"})
		return
	}

	appLogger.Info("Listed plugins successfully")
	c.JSON(http.StatusOK, gin.H{"plugins": plugins})
}

// apiGetPlugin retrieves a specific plugin by name
func apiGetPlugin(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/plugins/:name")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get plugin name from URL parameter
	pluginName := c.Param("name")
	if pluginName == "" {
		appLogger.Info("Plugin name is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plugin name is required"})
		return
	}

	// Get plugin from database
	var plugin models.Plugin
	db.Where("name = ?", pluginName).First(&plugin)

	// If plugin not found in database, return default information
	if plugin.ID == 0 {
		// Try to get plugin info from plugin engine
		info, err := pluginEngine.GetPluginInfo(pluginName)
		if err != nil {
			appLogger.Info("Plugin not found: " + pluginName)
			c.JSON(http.StatusNotFound, gin.H{"error": "Plugin not found"})
			return
		}

		// Create a default plugin object
		plugin = models.Plugin{
			Name:    info.Name,
			Version: info.Version,
			Target:  "example.com", // Default target
		}
	}

	appLogger.Info("Retrieved plugin successfully: " + pluginName)
	c.JSON(http.StatusOK, plugin)
}

// apiGetPluginInfo gets information about a specific plugin
func apiGetPluginInfo(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/plugins/:name/info")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get plugin name from URL parameter
	pluginName := c.Param("name")
	if pluginName == "" {
		appLogger.Info("Plugin name is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plugin name is required"})
		return
	}

	// Get plugin info
	info, err := pluginEngine.GetPluginInfo(pluginName)
	if err != nil {
		appLogger.Error("Failed to get plugin info: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get plugin info"})
		return
	}

	appLogger.Info("Retrieved plugin info successfully: " + pluginName)
	c.JSON(http.StatusOK, info)
}

// apiRunPlugin runs a specific plugin
func apiRunPlugin(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/plugins/:name/run")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get plugin name from URL parameter
	pluginName := c.Param("name")
	if pluginName == "" {
		appLogger.Info("Plugin name is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plugin name is required"})
		return
	}

	// Parse request body
	var req struct {
		Target string                 `json:"target"`
		Email  *plugin.EmailData      `json:"email"`
		Params map[string]interface{} `json:"params"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		appLogger.Error("Failed to bind plugin run request: " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Run plugin
	request := plugin.PluginRequest{
		Name:   pluginName,
		Target: req.Target,
		Email:  req.Email,
		Params: req.Params,
	}
	response, err := pluginEngine.RunPlugin(request)
	if err != nil {
		appLogger.Error("Failed to run plugin: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to run plugin"})
		return
	}

	appLogger.Info("Ran plugin successfully: " + pluginName)
	c.JSON(http.StatusOK, response)
}

// apiAddScheduledTask adds a phishing task to the scheduler
func apiAddScheduledTask(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/scheduler/tasks")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Parse request body
	var req struct {
		ID   string `json:"id" binding:"required"`
		Name string `json:"name" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		appLogger.Error("Failed to bind scheduled task creation request: " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// In the simplified scheduler, we don't add tasks this way.
	// Tasks are added via apiCreateTask which calls AddPhishingTask.
	// This endpoint is kept for API compatibility but will return an error.
	c.JSON(http.StatusBadRequest, gin.H{"error": "Use POST /api/v1/tasks to create phishing tasks"})
}

// apiRunScheduledTask runs a specific scheduled phishing task
func apiRunScheduledTask(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/scheduler/tasks/:id/run")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get task ID from URL parameter
	taskID := c.Param("id")
	if taskID == "" {
		appLogger.Info("Task ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// Run phishing task
	err = phishingTaskScheduler.RunPhishingTask(taskID)
	if err != nil {
		appLogger.Error("Failed to run phishing task: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to run phishing task"})
		return
	}

	appLogger.Info("Ran phishing task successfully: " + taskID)
	c.JSON(http.StatusOK, gin.H{"message": "Task executed successfully"})
}

// apiScheduleTask schedules a phishing task
func apiScheduleTask(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/scheduler/tasks/:id/schedule")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get task ID from URL parameter
	taskID := c.Param("id")
	if taskID == "" {
		appLogger.Info("Task ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// Parse request body
	var req struct {
		Interval    int64  `json:"interval" binding:"required"`
		StartTime   string `json:"start_time"`
		EndTime     string `json:"end_time"`
		RepeatCount int    `json:"repeat_count"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		appLogger.Error("Failed to bind task scheduling request: " + err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse time strings if provided
	var startTime, endTime time.Time
	if req.StartTime != "" {
		startTime, err = time.Parse(time.RFC3339, req.StartTime)
		if err != nil {
			appLogger.Error("Invalid start time format: " + err.Error())
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start time format"})
			return
		}
	}

	if req.EndTime != "" {
		endTime, err = time.Parse(time.RFC3339, req.EndTime)
		if err != nil {
			appLogger.Error("Invalid end time format: " + err.Error())
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end time format"})
			return
		}
	}

	// Schedule phishing task
	err = phishingTaskScheduler.SchedulePhishingTask(taskID, time.Duration(req.Interval)*time.Second, startTime, endTime, req.RepeatCount)
	if err != nil {
		appLogger.Error("Failed to schedule phishing task: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to schedule phishing task"})
		return
	}

	appLogger.Info("Scheduled phishing task successfully: " + taskID)
	c.JSON(http.StatusOK, gin.H{"message": "Task scheduled successfully"})
}

// apiGetScheduledTaskStatus gets the status of a scheduled phishing task
func apiGetScheduledTaskStatus(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/scheduler/tasks/:id/status")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get task ID from URL parameter
	taskID := c.Param("id")
	if taskID == "" {
		appLogger.Info("Task ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// Get scheduled phishing task status
	task, err := phishingTaskScheduler.GetScheduledPhishingTaskStatus(taskID)
	if err != nil {
		appLogger.Error("Failed to get scheduled phishing task status: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get scheduled phishing task status"})
		return
	}

	appLogger.Info("Retrieved scheduled phishing task status successfully: " + taskID)
	c.JSON(http.StatusOK, task)
}

// apiListScheduledTasks lists all scheduled phishing tasks
func apiListScheduledTasks(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/scheduler/tasks")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get list of scheduled phishing tasks
	tasks := phishingTaskScheduler.ListScheduledPhishingTasks()

	appLogger.Info("Listed scheduled phishing tasks successfully")
	c.JSON(http.StatusOK, tasks)
}

func apiGetDashboardData(c *gin.Context) {
	// Check JWT token
	authHeader := c.GetHeader("Authorization")
	tokenString := extractToken(authHeader)
	if tokenString == "" {
		appLogger.Info("Missing or invalid Authorization header")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	_, err := auth.ParseToken(tokenString)
	if err != nil {
		appLogger.Info("Unauthorized access to /api/v1/dashboard/data")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get tasks from database
	var tasks []models.Task
	db.Find(&tasks)

	// Get plugins from database
	var plugins []models.Plugin
	db.Find(&plugins)

	// Get scheduled phishing tasks
	scheduledTasks := phishingTaskScheduler.ListScheduledPhishingTasks()

	// Prepare dashboard data
	dashboardData := struct {
		Tasks          []models.Task                      `json:"tasks"`
		Plugins        []models.Plugin                    `json:"plugins"`
		ScheduledTasks []*scheduler.ScheduledPhishingTask `json:"scheduled_tasks"`
	}{
		Tasks:          tasks,
		Plugins:        plugins,
		ScheduledTasks: scheduledTasks,
	}

	appLogger.Info("Retrieved dashboard data successfully")
	c.JSON(http.StatusOK, dashboardData)
}

// Web handler functions
func webIndex(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", gin.H{})
}

func webLogin(c *gin.Context) {
	c.HTML(http.StatusOK, "login.html", gin.H{})
}

func webRegister(c *gin.Context) {
	c.HTML(http.StatusOK, "register.html", gin.H{})
}

func webDashboard(c *gin.Context) {
	c.HTML(http.StatusOK, "dashboard.html", gin.H{})
}

func webAgentManagement(c *gin.Context) {
	c.HTML(http.StatusOK, "agent_management.html", gin.H{})
}

func webPluginManagement(c *gin.Context) {
	c.HTML(http.StatusOK, "plugin_management.html", gin.H{})
}

func webEmailTest(c *gin.Context) {
	c.HTML(http.StatusOK, "email_test.html", gin.H{})
}

func webScheduler(c *gin.Context) {
	c.HTML(http.StatusOK, "scheduler.html", gin.H{})
}

func webTasks(c *gin.Context) {
	c.HTML(http.StatusOK, "tasks.html", gin.H{})
}

// Helper function to generate a unique task ID
func generateTaskID() uint {
	// In a real implementation, you would generate a proper unique ID
	// For now, we'll use a simple timestamp-based ID
	// Convert timestamp to uint
	timestamp := time.Now().Unix()
	return uint(timestamp)
}
