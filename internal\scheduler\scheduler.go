package scheduler

import (
	"fmt"
	"log"
	"sync"
	"time"
)

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"
)

// Task represents a scheduled task
type Task struct {
	ID       string
	Name     string
	Function func() error
	Status   TaskStatus
	Created  time.Time
	Started  time.Time
	Finished time.Time
	Error    error
}

// TaskScheduler manages task scheduling
type TaskScheduler struct {
	tasks   map[string]*Task
	mutex   sync.Mutex
	workers map[string]chan struct{} // Used to cancel scheduled tasks
}

// NewTaskScheduler creates a new task scheduler
func NewTaskScheduler() *TaskScheduler {
	return &TaskScheduler{
		tasks:   make(map[string]*Task),
		workers: make(map[string]chan struct{}),
	}
}

// AddTask adds a task to the scheduler
func (s *TaskScheduler) AddTask(id, name string, taskFunc func() error) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Check if task already exists
	if _, exists := s.tasks[id]; exists {
		return fmt.E<PERSON>rf("task with ID %s already exists", id)
	}

	// Create new task
	task := &Task{
		ID:       id,
		Name:     name,
		Function: taskFunc,
		Status:   TaskStatusPending,
		Created:  time.Now(),
	}

	// Add task to scheduler
	s.tasks[id] = task
	log.Printf("Added task %s with ID %s", name, id)
	return nil
}

// RunTask runs a specific task by ID
func (s *TaskScheduler) RunTask(id string) error {
	s.mutex.Lock()
	task, exists := s.tasks[id]
	s.mutex.Unlock()

	if !exists {
		return fmt.Errorf("task with ID %s not found", id)
	}

	// Update task status
	s.mutex.Lock()
	task.Status = TaskStatusRunning
	task.Started = time.Now()
	s.mutex.Unlock()

	log.Printf("Running task %s with ID %s", task.Name, task.ID)

	// Run task function
	err := task.Function()

	// Update task status based on result
	s.mutex.Lock()
	if err != nil {
		task.Status = TaskStatusFailed
		task.Error = err
		log.Printf("Task %s with ID %s failed: %v", task.Name, task.ID, err)
	} else {
		task.Status = TaskStatusCompleted
		log.Printf("Task %s with ID %s completed successfully", task.Name, task.ID)
	}
	task.Finished = time.Now()
	s.mutex.Unlock()

	return err
}

// ScheduleTask schedules a task to run at a specific interval
func (s *TaskScheduler) ScheduleTask(id string, interval time.Duration) error {
	s.mutex.Lock()
	task, exists := s.tasks[id]
	s.mutex.Unlock()

	if !exists {
		return fmt.Errorf("task with ID %s not found", id)
	}

	// Check if task is already scheduled
	s.mutex.Lock()
	_, scheduled := s.workers[id]
	s.mutex.Unlock()

	if scheduled {
		return fmt.Errorf("task with ID %s is already scheduled", id)
	}

	log.Printf("Scheduling task %s with ID %s to run every %v", task.Name, task.ID, interval)

	// Create a channel to control the worker
	stopChan := make(chan struct{})
	s.mutex.Lock()
	s.workers[id] = stopChan
	s.mutex.Unlock()

	// Start worker goroutine
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// Run the task
				s.RunTask(id)
			case <-stopChan:
				// Stop the worker
				log.Printf("Cancelled scheduled task %s with ID %s", task.Name, task.ID)
				return
			}
		}
	}()

	return nil
}

// CancelScheduledTask cancels a scheduled task
func (s *TaskScheduler) CancelScheduledTask(id string) error {
	s.mutex.Lock()
	task, exists := s.tasks[id]
	stopChan, scheduled := s.workers[id]
	s.mutex.Unlock()

	if !exists {
		return fmt.Errorf("task with ID %s not found", id)
	}

	if !scheduled {
		return fmt.Errorf("task with ID %s is not scheduled", id)
	}

	// Close the stop channel to signal the worker to stop
	close(stopChan)

	// Remove the worker from the map
	s.mutex.Lock()
	delete(s.workers, id)
	s.mutex.Unlock()

	// Update task status
	s.mutex.Lock()
	task.Status = TaskStatusCancelled
	s.mutex.Unlock()

	log.Printf("Cancelled scheduled task %s with ID %s", task.Name, task.ID)
	return nil
}

// GetTaskStatus returns the status of a task
func (s *TaskScheduler) GetTaskStatus(id string) (*Task, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	task, exists := s.tasks[id]
	if !exists {
		return nil, fmt.Errorf("task with ID %s not found", id)
	}

	// Return a copy of the task to avoid race conditions
	taskCopy := *task
	return &taskCopy, nil
}

// ListTasks returns a list of all tasks
func (s *TaskScheduler) ListTasks() []*Task {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	tasks := make([]*Task, 0, len(s.tasks))
	for _, task := range s.tasks {
		// Return a copy of the task to avoid race conditions
		taskCopy := *task
		tasks = append(tasks, &taskCopy)
	}

	return tasks
}