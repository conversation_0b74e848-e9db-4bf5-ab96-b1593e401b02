package scheduler\n\nimport (\n\t\"fmt\"\n\t\"time\"\n)\n\n// TaskScheduler manages task scheduling\ntype TaskScheduler struct {\n\ttasks map[string]func()\n}\n\n// NewTaskScheduler creates a new task scheduler\nfunc NewTaskScheduler() *TaskScheduler {\n\treturn &TaskScheduler{\n\t\ttasks: make(map[string]func()),\n\t}\n}\n\n// AddTask adds a task to the scheduler\nfunc (s *TaskScheduler) AddTask(name string, task func()) {\n\ts.tasks[name] = task\n}\n\n// RunTask runs a specific task by name\nfunc (s *TaskScheduler) RunTask(name string) error {\n\ttask, exists := s.tasks[name]\n\tif !exists {\n\t\treturn fmt.Errorf(\"task %s not found\", name)\n\t}\n\ttask()\n\treturn nil\n}\n\n// ScheduleTask schedules a task to run at a specific interval\nfunc (s *TaskScheduler) ScheduleTask(name string, interval time.Duration) {\n\ttask, exists := s.tasks[name]\n\tif !exists {\n\t\tfmt.Printf(\"Task %s not found\\n\", name)\n\t\treturn\n\t}\n\n\tticker := time.NewTicker(interval)\n\tgo func() {\n\t\tfor {\n\t\t\tselect {\n\t\t\tcase <-ticker.C:\n\t\t\t\ttask()\n\t\t\t}\n\t\t}\n\t}()\n}