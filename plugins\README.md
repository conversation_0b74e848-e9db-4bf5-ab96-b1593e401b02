# 插件开发指南

## 插件规范

插件需要遵循标准输入/输出（JSON）格式。

### 输入格式

插件接收以下JSON格式的输入：

```json
{
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {
    "参数名": "参数值"
  }
}
```

### 输出格式

插件需要输出以下JSON格式的结果：

```json
{
  "status": "success|error",
  "email": {
    // 修改后的邮件数据（可选）
  },
  "detail": "详细信息"
}
```

## 插件开发步骤

1. 创建一个新的Python文件，文件名将作为插件名
2. 实现`run(target, email, params)`函数处理邮件数据
3. 实现`get_info()`函数返回插件信息
4. 在`__main__`块中处理输入输出

## 示例插件

```python
import json
import sys

def get_info():
    # Return plugin information
    info = {
        "name": "example_plugin",
        "description": "插件描述",
        "version": "1.0.0"
    }
    return info

def run(target, email, params):
    # 获取操作类型
    action = params.get("action")
    
    # 如果是info操作，返回插件信息
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # 插件核心逻辑
    # 在这里实现具体的邮件处理逻辑
    result = {
        "status": "success",
        "detail": "插件执行成功"
    }
    return result

if __name__ == "__main__":
    # 从stdin读取输入
    data = json.loads(sys.stdin.read())
    
    # 提取目标、邮件数据和参数
    target = data.get("target")
    email = data.get("email", {})
    params = data.get("params", {})
    
    # 执行插件逻辑
    res = run(target, email, params)
    
    # 输出结果到stdout
    print(json.dumps(res))
```

## 现有插件

### SPF绕过插件 (spf_bypass.py)

该插件通过分离`MAIL FROM`命令和邮件头部的`From`字段实现SPF绕过。

**参数**:
- `mail_from`: 用于SMTP `MAIL FROM`命令的邮箱地址

### DKIM签名伪造插件 (dkim_forgery.py)

该插件生成伪造的DKIM签名并添加到邮件头部。

**参数**:
- `domain`: DKIM域名
- `selector`: DKIM选择器
- `private_key`: 私钥（演示用）

### 视觉欺骗插件 (visual_deception.py)

该插件使用Unicode RTL覆盖字符创建视觉欺骗的发件人地址。

**参数**:
- `target_domain`: 目标域名
- `deceptive_domain`: 欺骗域名

### SMTP走私插件 (smtp_smuggling.py)

该插件在邮件头部添加SMTP走私指令。

**参数**:
- `smuggled_from`: 走私的发件人地址

### 表情符号域名劫持插件 (emoji_domain_hijacking.py)

该插件通过注册包含表情符号的相似域名实现欺骗。

**参数**:
- `target_domain`: 目标域名
- `emoji_char`: 用于混淆的表情符号字符

### SVG代码执行插件 (svg_code_execution.py)

该插件在邮件中嵌入恶意SVG代码实现代码执行。

**参数**:
- `payload_url`: 用于接收窃取数据的恶意服务器URL
- `svg_type`: SVG类型（image, script, 或 onload）
- `filename`: 生成的SVG文件名

### 会话劫持模块插件 (session_hijacking.py)

该插件通过恶意链接跟踪用户点击行为。

**参数**:
- `malicious_domain`: 恶意服务器域名
- `tracking_path`: 跟踪路径
- `additional_params`: 额外的跟踪参数
- `tracking_link_text`: 跟踪链接显示文本

## Agent交互

插件不需要直接与Agent交互，但了解Agent的工作机制有助于开发更好的插件。

### Agent选择机制

系统支持多个邮件发送代理（Agent），并实现了基于健康检查的智能Agent选择机制：

1. **健康检查**：在选择Agent之前，系统会对所有已注册的Agent进行健康检查
   - 检查Agent的状态（连接、断开、停止、错误等）
   - 验证Agent的连通性
   - 确认Agent的功能是否正常

2. **随机选择**：从健康的Agent中随机选择一个来执行任务
   - 如果只有一个健康的Agent，则选择该Agent
   - 如果有多个健康的Agent，则随机选择其中一个
   - 如果没有健康的Agent，则返回错误

3. **故障隔离**：自动避开不健康的Agent，提高任务执行的成功率

### 插件开发建议

1. **邮件数据处理**：插件应专注于处理邮件数据，不要尝试与Agent直接通信
2. **错误处理**：插件应妥善处理各种错误情况，并返回适当的错误信息
3. **性能考虑**：插件应尽量高效执行，避免长时间阻塞
4. **安全性**：插件不应包含恶意代码，应专注于安全测试目的

## 多插件处理

框架支持按顺序调用多个插件处理邮件数据：
1. 用户提交任务时指定插件列表及其顺序
2. 框架按顺序调用每个插件
3. 每个插件接收上一个插件处理后的邮件数据
4. 最后一个插件处理完成后，将最终的邮件数据发送给通过健康检查随机选择的Agent

这种设计使得可以组合使用多种伪造技术，例如先进行SPF绕过，再添加DKIM签名，最后进行视觉欺骗。

## 详细使用文档

有关每个插件的详细使用说明，请参阅 [USAGE.md](USAGE.md) 文件。

## 插件系统使用说明

有关插件系统架构和开发指南的详细信息，请参阅 [Dev.md](Dev.md) 文件。