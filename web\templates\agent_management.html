<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent管理 - Phish X</title>
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Materialize CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --accent-color: #FF9800;
            --warn-color: #F44336;
            --background-color: #f5f5f5;
            --surface-color: #ffffff;
            --text-primary: rgba(0, 0, 0, 0.87);
            --text-secondary: rgba(0, 0, 0, 0.54);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .sidenav {
            background-color: var(--surface-color);
        }

        .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            margin-bottom: 1rem;
        }

        .card-content {
            padding: 16px;
        }

        .btn-primary {
            background-color: var(--primary-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-accent {
            background-color: var(--accent-color);
        }

        .btn-warn {
            background-color: var(--warn-color);
        }

        .page-header {
            padding: 1rem 0;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(0,0,0,.1);
        }

        .page-title {
            font-weight: 500;
            margin: 0;
        }

        .breadcrumb {
            font-size: 1rem;
        }

        .breadcrumb:before {
            color: var(--text-secondary);
        }

        .breadcrumb:last-child {
            color: var(--text-primary);
        }

        .table-responsive {
            overflow-x: auto;
        }

        .data-table th {
            font-weight: 500;
            color: var(--text-primary);
        }

        .data-table td {
            color: var(--text-secondary);
        }

        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-primary {
            color: #fff;
            background-color: var(--primary-color);
        }

        .badge-secondary {
            color: #fff;
            background-color: var(--secondary-color);
        }

        .badge-accent {
            color: #fff;
            background-color: var(--accent-color);
        }

        .badge-warn {
            color: #fff;
            background-color: var(--warn-color);
        }

        .badge-success {
            color: #fff;
            background-color: #4CAF50;
        }

        .badge-danger {
            color: #fff;
            background-color: #F44336;
        }

        .badge-warning {
            color: #fff;
            background-color: #FF9800;
        }

        .loader {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .toast {
            border-radius: 4px;
        }

        .footer {
            padding: 1rem 0;
            background-color: var(--surface-color);
            border-top: 1px solid rgba(0,0,0,.1);
        }

        .footer-links a {
            color: var(--text-secondary);
            margin-right: 1rem;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-wrapper">
            <a href="/" class="brand-logo">
                <i class="material-icons left" style="margin-left: 10px;">security</i>
                Phish X
            </a>
            <a href="#" data-target="mobile-nav" class="sidenav-trigger">
                <i class="material-icons">menu</i>
            </a>
            <ul class="right hide-on-med-and-down">
                <li><a href="/tasks">任务管理</a></li>
                <li><a href="/plugin-management">插件管理</a></li>
                <li><a href="/email-test">邮件测试</a></li>
                <li><a href="/scheduler">任务调度</a></li>
                <li><a href="/dashboard">数据展示</a></li>
                <li class="active"><a href="/agent-management">Agent管理</a></li>
                <li id="authSection">
                    <!-- Auth buttons will be inserted here by JavaScript -->
                </li>
            </ul>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <ul class="sidenav" id="mobile-nav">
        <li><a href="/tasks">任务管理</a></li>
        <li><a href="/plugin-management">插件管理</a></li>
        <li><a href="/email-test">邮件测试</a></li>
        <li><a href="/scheduler">任务调度</a></li>
        <li><a href="/dashboard">数据展示</a></li>
        <li class="active"><a href="/agent-management">Agent管理</a></li>
        <li id="mobileAuthSection">
            <!-- Auth buttons will be inserted here by JavaScript -->
        </li>
    </ul>

    <!-- Breadcrumbs -->
    <nav class="breadcrumb-container">
        <div class="nav-wrapper">
            <div class="col s12">
                <a href="/" class="breadcrumb">首页</a>
                <a href="/agent-management" class="breadcrumb">Agent管理</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="page-header">
                        <h2 class="page-title">Agent管理</h2>
                        <p class="lead">管理邮件发送Agent，包括注册、状态监控和任务分发。</p>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">已注册的Agent</h5>
                            <button class="btn btn-sm btn-outline-primary" id="refreshAgents">
                                <i class="material-icons left">refresh</i> 刷新
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>API端点</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="agentTableBody">
                                        <!-- Agent数据将通过JavaScript动态加载 -->
                                        <tr>
                                            <td colspan="4" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">注册新Agent</h5>
                        </div>
                        <div class="card-body">
                            <form id="registerAgentForm">
                                <div class="form-group">
                                    <label for="agentId">Agent ID</label>
                                    <input type="text" class="form-control" id="agentId" required>
                                </div>
                                <div class="form-group">
                                    <label for="apiEndpoint">API端点</label>
                                    <input type="text" class="form-control" id="apiEndpoint" placeholder="http://localhost:8081/api/v1" required>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="material-icons left">save</i> 注册Agent
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="page-footer">
        <div class="container">
            <div class="row">
                <div class="col l6 s12">
                    <h5 class="white-text">Phish X</h5>
                    <p class="grey-text text-lighten-4">一个轻便型、插件化、可扩展的社会工程安全测试平台。</p>
                </div>
                <div class="col l4 offset-l2 s12">
                    <h5 class="white-text">链接</h5>
                    <ul class="footer-links">
                        <li><a class="grey-text text-lighten-3" href="/">首页</a></li>
                        <li><a class="grey-text text-lighten-3" href="/docs">文档</a></li>
                        <li><a class="grey-text text-lighten-3" href="/support">支持</a></li>
                        <li><a class="grey-text text-lighten-3" href="/about">关于</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-copyright">
            <div class="container">
                © 2023 Phish X. 保留所有权利。
                <a class="grey-text text-lighten-4 right" href="#!">隐私政策</a>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Materialize components
            $('.sidenav').sidenav();
            $('.modal').modal();
            $('.collapsible').collapsible();
            $('.tabs').tabs();
            $('.tooltipped').tooltip();
            $('select').formSelect();
            
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (!token) {
                // Redirect to login page if not logged in
                window.location.href = '/login';
                return;
            }
            
            // Show user info and logout button
            $('#authSection').html(`
                <li class="nav-item">
                    <a class="nav-link waves-effect waves-light btn red lighten-1" href="#" id="logoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#mobileAuthSection').html(`
                <li>
                    <a href="#" id="mobileLogoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#logoutBtn, #mobileLogoutBtn').click(function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                window.location.href = '/login';
            });
            
            // Load agents
            loadAgents();
            
            // Refresh agents when button is clicked
            $('#refreshAgents').click(function() {
                loadAgents();
            });
            
            // Handle form submission
            $('#registerAgentForm').submit(function(e) {
                e.preventDefault();
                registerAgent();
            });
        });
        
        function loadAgents() {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: '/api/v1/agents',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(result) {
                    // Clear table
                    $('#agentTableBody').empty();
                    
                    // Check if there are agents
                    if (!result.agents || result.agents.length === 0) {
                        $('#agentTableBody').html('<tr><td colspan="4" class="text-center">暂无注册的Agent</td></tr>');
                        return;
                    }
                    
                    // Add agents to table
                    result.agents.forEach(agentId => {
                        // Get agent details
                        $.ajax({
                            url: `/api/v1/agents/${encodeURIComponent(agentId)}`,
                            method: 'GET',
                            headers: {
                                'Authorization': 'Bearer ' + token
                            },
                            success: function(agent) {
                                const statusBadge = agent.status === 'idle' ? 
                                    '<span class="badge badge-success">空闲</span>' : 
                                    agent.status === 'sending' ? 
                                    '<span class="badge badge-warning">发送中</span>' : 
                                    '<span class="badge badge-danger">未知</span>';
                                    
                                $('#agentTableBody').append(`
                                    <tr>
                                        <td>${agent.id}</td>
                                        <td>${agent.api_endpoint}</td>
                                        <td>${statusBadge}</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary waves-effect waves-light" onclick="viewAgent('${agent.id}')">
                                                <i class="material-icons">visibility</i>
                                            </button>
                                            <button class="btn btn-sm btn-danger waves-effect waves-light" onclick="deleteAgent('${agent.id}')">
                                                <i class="material-icons">delete</i>
                                            </button>
                                        </td>
                                    </tr>
                                `);
                            },
                            error: function(xhr, status, error) {
                                console.log('获取Agent详情失败: ' + agentId);
                                // Still add the agent to the table with minimal info
                                $('#agentTableBody').append(`
                                    <tr>
                                        <td>${agentId}</td>
                                        <td>未知</td>
                                        <td><span class="badge badge-danger">错误</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary waves-effect waves-light" onclick="viewAgent('${agentId}')">
                                                <i class="material-icons">visibility</i>
                                            </button>
                                            <button class="btn btn-sm btn-danger waves-effect waves-light" onclick="deleteAgent('${agentId}')">
                                                <i class="material-icons">delete</i>
                                            </button>
                                        </td>
                                    </tr>
                                `);
                            }
                        });
                    });
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        $('#agentTableBody').html('<tr><td colspan="4" class="text-center">加载Agent列表失败</td></tr>');
                        M.toast({html: '加载Agent列表失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                        console.log('加载Agent列表失败: ' + xhr.responseJSON.error);
                    }
                }
            });
        }
        
        function registerAgent() {
            const token = localStorage.getItem('token');
            
            const agentData = {
                id: $('#agentId').val(),
                api_endpoint: $('#apiEndpoint').val()
            };
            
            $.ajax({
                url: '/api/v1/agents',
                method: 'POST',
                contentType: 'application/json',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                data: JSON.stringify(agentData),
                success: function(result) {
                    M.toast({html: 'Agent注册成功', classes: 'rounded green'});
                    $('#registerAgentForm')[0].reset();
                    loadAgents();
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: 'Agent注册失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
        }
        
        function viewAgent(agentId) {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: `/api/v1/agents/${encodeURIComponent(agentId)}`,
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(agent) {
                    // Create a modal to display agent details
                    let modalHtml = `
                        <div id="agentModal" class="modal">
                            <div class="modal-content">
                                <h4>Agent详情</h4>
                                <table class="striped">
                                    <tr><td><strong>ID:</strong></td><td>${agent.id}</td></tr>
                                    <tr><td><strong>API端点:</strong></td><td>${agent.api_endpoint}</td></tr>
                                    <tr><td><strong>状态:</strong></td><td>${agent.status}</td></tr>
                                    <tr><td><strong>版本:</strong></td><td>${agent.version || '未知'}</td></tr>
                                    <tr><td><strong>最后在线:</strong></td><td>${agent.last_seen || '未知'}</td></tr>
                                </table>
                            </div>
                            <div class="modal-footer">
                                <a href="#!" class="modal-close waves-effect waves-green btn-flat">关闭</a>
                            </div>
                        </div>
                    `;
                    
                    // Append modal to body and open it
                    $('body').append(modalHtml);
                    $('#agentModal').modal();
                    $('#agentModal').modal('open');
                    
                    // Remove modal from DOM when closed
                    $('#agentModal').on('modal-close', function() {
                        $('#agentModal').remove();
                    });
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: '获取Agent详情失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
        }
        
        function deleteAgent(agentId) {
            if (!confirm(`确定要删除Agent "${agentId}"吗？`)) {
                return;
            }
            
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: `/api/v1/agents/${encodeURIComponent(agentId)}`,
                method: 'DELETE',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(result) {
                    M.toast({html: 'Agent删除成功', classes: 'rounded green'});
                    loadAgents();
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: '删除Agent失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
        }
    </script>
</body>
</html>