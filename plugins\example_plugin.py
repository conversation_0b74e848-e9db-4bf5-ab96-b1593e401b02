import json
import sys

def get_info():
    # Return plugin information
    info = {
        "name": "example_plugin",
        "description": "An example plugin for AI Phish",
        "version": "1.0.0"
    }
    return info

def run(target, params):
    # Example plugin logic
    # In a real plugin, this would perform some vulnerability check
    action = params.get("action")
    
    # If action is 'info', return plugin information
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # Otherwise, execute normal plugin logic
    result = {
        "status": "success",
        "detail": f"Executed plugin on {target} with params {params}"
    }
    return result

if __name__ == "__main__":
    # Read input from stdin
    data = json.loads(sys.stdin.read())
    
    # Extract target and parameters
    target = data.get("target")
    params = data.get("params", {})
    
    # Run the plugin logic
    res = run(target, params)
    
    # Output result to stdout
    print(json.dumps(res))