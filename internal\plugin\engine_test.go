package plugin

import (
	"os"
	"strings"
	"testing"
)

// Mock plugin for testing
const mockPluginContent = `
import json
import sys

def run(target, params):
    result = {
        "status": "success",
        "detail": f"Executed plugin on {target} with params {params}"
    }
    return result

if __name__ == "__main__":
    # Read input from stdin
    data = json.loads(sys.stdin.read())
    
    # Extract target and parameters
    target = data.get("target")
    params = data.get("params", {})
    
    # Run the plugin logic
    res = run(target, params)
    
    # Output result to stdout
    print(json.dumps(res))
`

func TestPluginEngine(t *testing.T) {
	// Create a temporary directory for plugins
	pluginDir := "./test_plugins"
	err := os.MkdirAll(pluginDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create plugin directory: %v", err)
	}
	defer os.RemoveAll(pluginDir)

	// Create a mock plugin file
	pluginFile := pluginDir + "/mock_plugin.py"
	err = os.WriteFile(pluginFile, []byte(mockPluginContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create mock plugin file: %v", err)
	}

	// Create a plugin engine
	engine := NewPluginEngine(pluginDir)

	// Test listing plugins
	plugins, err := engine.ListPlugins()
	if err != nil {
		t.Errorf("Failed to list plugins: %v", err)
	}

	// Check that we have at least one plugin
	if len(plugins) == 0 {
		t.Error("Expected at least one plugin")
	}

	// Check that our mock plugin is in the list
	found := false
	for _, plugin := range plugins {
		if plugin == "mock_plugin" {
			found = true
			break
		}
	}
	if !found {
		t.Error("Expected mock_plugin in plugin list")
	}

	// Test running a plugin
	request := PluginRequest{
		Name:   "mock_plugin",
		Target: "example.com",
		Params: map[string]interface{}{
			"param1": "value1",
			"param2": "value2",
		},
	}

	response, err := engine.RunPlugin(request)
	if err != nil {
		t.Errorf("Failed to run plugin: %v", err)
	}

	// Check response
	if response.Status != "success" {
		t.Errorf("Expected status 'success', got '%s'", response.Status)
	}

	// Check that the response detail contains the target and params
	responseDetail := response.Detail
	expectedTarget := "example.com"
	expectedParam1 := "value1"
	expectedParam2 := "value2"

	if responseDetail == "" {
		t.Error("Response detail is empty")
	}

	// Check that the response detail contains the expected values
	if !strings.Contains(responseDetail, expectedTarget) {
		t.Errorf("Response detail does not contain target '%s'", expectedTarget)
	}

	if !strings.Contains(responseDetail, expectedParam1) {
		t.Errorf("Response detail does not contain param1 '%s'", expectedParam1)
	}

	if !strings.Contains(responseDetail, expectedParam2) {
		t.Errorf("Response detail does not contain param2 '%s'", expectedParam2)
	}
}