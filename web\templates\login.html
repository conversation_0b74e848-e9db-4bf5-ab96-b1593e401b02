<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - Phish X</title>
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Materialize CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --accent-color: #FF9800;
            --warn-color: #F44336;
            --background-color: #f5f5f5;
            --surface-color: #ffffff;
            --text-primary: rgba(0, 0, 0, 0.87);
            --text-secondary: rgba(0, 0, 0, 0.54);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-card {
            width: 100%;
            max-width: 400px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .login-header {
            padding: 20px;
            text-align: center;
            background-color: var(--primary-color);
            color: white;
            border-radius: 8px 8px 0 0;
        }

        .login-body {
            padding: 20px;
        }

        .input-field {
            margin-top: 1rem;
            margin-bottom: 1rem;
        }

        .btn-login {
            width: 100%;
            background-color: var(--primary-color);
        }

        .login-footer {
            text-align: center;
            padding: 10px;
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="login-card">
                <div class="login-header">
                    <h4>Phish X 登录</h4>
                    <p>请输入您的凭证以访问平台</p>
                </div>
                <div class="login-body">
                    <form id="loginForm">
                        <div class="input-field">
                            <i class="material-icons prefix">account_circle</i>
                            <input id="username" type="text" class="validate" required>
                            <label for="username">用户名</label>
                        </div>
                        <div class="input-field">
                            <i class="material-icons prefix">lock</i>
                            <input id="password" type="password" class="validate" required>
                            <label for="password">密码</label>
                        </div>
                        <button class="btn btn-login waves-effect waves-light" type="submit" name="action">
                            登录
                            <i class="material-icons right">send</i>
                        </button>
                    </form>
                </div>
                <div class="login-footer">
                    <p>还没有账户? <a href="/register">立即注册</a></p>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Materialize components
            $('input').characterCounter();
            
            // Handle form submission
            $('#loginForm').submit(function(e) {
                e.preventDefault();
                
                const username = $('#username').val();
                const password = $('#password').val();
                
                // Send login request
                $.ajax({
                    url: '/api/v1/login',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        username: username,
                        password: password
                    }),
                    success: function(result) {
                        // Store token in localStorage
                        localStorage.setItem('token', result.token);
                        
                        // Redirect to the original requested page or dashboard
                        const urlParams = new URLSearchParams(window.location.search);
                        const redirect = urlParams.get('redirect');
                        if (redirect) {
                            window.location.href = redirect;
                        } else {
                            window.location.href = '/dashboard';
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('登录失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误'));
                    }
                });
            });
        });
    </script>
</body>
</html>