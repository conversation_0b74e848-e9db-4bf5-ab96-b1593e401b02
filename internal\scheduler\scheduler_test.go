package scheduler

import (
	"errors"
	"testing"
	"time"
)

func TestTaskScheduler(t *testing.T) {
	// Create a new task scheduler
	scheduler := NewTaskScheduler()

	// Test adding a task
	taskID := "test-task"
	taskName := "Test Task"
	taskFunc := func() error {
		// Simulate some work
		time.Sleep(100 * time.Millisecond)
		return nil
	}

	err := scheduler.AddTask(taskID, taskName, taskFunc)
	if err != nil {
		t.Errorf("Failed to add task: %v", err)
	}

	// Test adding a duplicate task
	err = scheduler.AddTask(taskID, taskName, taskFunc)
	if err == nil {
		t.Error("Expected error when adding duplicate task")
	}

	// Test running a task
	err = scheduler.RunTask(taskID)
	if err != nil {
		t.Errorf("Failed to run task: %v", err)
	}

	// Test running a non-existent task
	err = scheduler.RunTask("non-existent-task")
	if err == nil {
		t.Error("Expected error when running non-existent task")
	}

	// Test getting task status
	task, err := scheduler.GetTaskStatus(taskID)
	if err != nil {
		t.Errorf("Failed to get task status: %v", err)
	}

	if task.ID != taskID {
		t.Errorf("Expected task ID %s, got %s", taskID, task.ID)
	}

	if task.Name != taskName {
		t.Errorf("Expected task name %s, got %s", taskName, task.Name)
	}

	if task.Status != TaskStatusCompleted {
		t.Errorf("Expected task status %s, got %s", TaskStatusCompleted, task.Status)
	}

	// Test listing tasks
	tasks := scheduler.ListTasks()
	if len(tasks) != 1 {
		t.Errorf("Expected 1 task, got %d", len(tasks))
	}

	// Test task with error
	errorTaskID := "error-task"
	errorTaskName := "Error Task"
	errorTaskFunc := func() error {
		return errors.New("task failed")
	}

	err = scheduler.AddTask(errorTaskID, errorTaskName, errorTaskFunc)
	if err != nil {
		t.Errorf("Failed to add error task: %v", err)
	}

	err = scheduler.RunTask(errorTaskID)
	if err == nil {
		t.Error("Expected error when running error task")
	}

	// Check error task status
	errorTask, err := scheduler.GetTaskStatus(errorTaskID)
	if err != nil {
		t.Errorf("Failed to get error task status: %v", err)
	}

	if errorTask.Status != TaskStatusFailed {
		t.Errorf("Expected error task status %s, got %s", TaskStatusFailed, errorTask.Status)
	}

	if errorTask.Error == nil {
		t.Error("Expected error task to have an error")
	}
}