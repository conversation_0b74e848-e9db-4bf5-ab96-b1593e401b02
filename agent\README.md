# 邮件发送代理 (Agent)

## 概述

Agent是一个纯粹的邮件发送引擎，负责实际发送邮件。它不包含任何邮件伪造逻辑，只负责执行发送操作。

## API接口

### 获取Agent信息

```
GET /api/v1/info
```

返回Agent的基本信息。

### 发送邮件

```
POST /api/v1/send
```

发送邮件到指定的收件人。

**请求体**:

```json
{
  "from": "发件人邮箱地址",
  "to": ["收件人邮箱地址列表"],
  "subject": "邮件主题",
  "body": "邮件正文",
  "headers": {"自定义头部字段": "值"},
  "tracking_id": "跟踪ID",
  "smtp_config": {
    "host": "SMTP服务器地址",
    "port": "SMTP服务器端口",
    "username": "用户名",
    "password": "密码"
  }
}
```

### 获取Agent状态

```
GET /api/v1/status
```

返回Agent的当前状态。

### 获取Agent统计信息

```
GET /api/v1/stats
```

返回Agent的统计信息。

## 部署

1. 编译Agent:
   ```
   go build -o agent main.go
   ```

2. 运行Agent:
   ```
   ./agent
   ```

Agent将在端口8081上启动HTTP服务器。

## 工作原理

Agent接收来自框架的完整邮件数据，包括：
- 发件人地址
- 收件人地址列表
- 邮件主题
- 邮件正文
- 自定义头部字段（包括插件添加的伪造信息）

Agent使用TLS加密连接到SMTP服务器，执行以下操作：
1. 连接到SMTP服务器
2. 进行身份验证（如果提供了用户名和密码）
3. 设置发件人地址（使用邮件数据中的`from`字段）
4. 设置收件人地址
5. 发送邮件内容

## 特性

- **纯粹的邮件发送引擎**：不包含任何伪造逻辑，所有伪造操作都由插件完成
- **TLS支持**：支持TLS加密的SMTP连接
- **身份验证**：支持SMTP身份验证
- **轻量级**：资源占用少，可部署多个实例
- **高可用**：支持多个Agent实例，框架会自动选择可用的Agent

## 注意事项

- Agent只负责发送插件已经处理好的邮件数据
- Agent不处理任何业务逻辑，只负责邮件发送
- Agent支持多个实例部署，提高发送能力和可靠性