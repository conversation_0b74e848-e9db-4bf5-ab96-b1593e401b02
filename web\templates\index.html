<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Phish - 社会工程安全测试平台</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <a class="navbar-brand" href="/">AI Phish</a>
        <div class="collapse navbar-collapse">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/tasks">任务管理</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/plugins">插件管理</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/plugin-management">插件管理</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/email-test">邮件测试</a>
                </li>
            </ul>
            <ul class="navbar-nav ml-auto" id="authSection">
                <!-- Auth buttons will be inserted here by JavaScript -->
            </ul>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="jumbotron">
            <h1 class="display-4">欢迎使用 AI Phish</h1>
            <p class="lead">这是一个轻便型、插件化、可扩展的社会工程安全测试平台。</p>
            <hr class="my-4">
            <p>请选择您要进行的操作。</p>
            <a class="btn btn-primary btn-lg" href="/tasks" role="button">任务管理</a>
            <a class="btn btn-secondary btn-lg" href="/plugins" role="button">插件管理</a>
            <a class="btn btn-info btn-lg" href="/plugin-management" role="button">插件管理</a>
            <a class="btn btn-warning btn-lg" href="/email-test" role="button">邮件测试</a>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (token) {
                // User is logged in, show user info and logout button
                $('#authSection').html(`
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logoutBtn">退出登录</a>
                    </li>
                `);
                
                $('#logoutBtn').click(function(e) {
                    e.preventDefault();
                    localStorage.removeItem('token');
                    location.reload();
                });
            } else {
                // User is not logged in, show login and register buttons
                $('#authSection').html(`
                    <li class="nav-item">
                        <a class="nav-link" href="/login">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/register">注册</a>
                    </li>
                `);
            }
        });
    </script>
</body>
</html>