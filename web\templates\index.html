<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phish X - 社会工程安全测试平台</title>
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Materialize CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --accent-color: #FF9800;
            --warn-color: #F44336;
            --background-color: #f5f5f5;
            --surface-color: #ffffff;
            --text-primary: rgba(0, 0, 0, 0.87);
            --text-secondary: rgba(0, 0, 0, 0.54);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .sidenav {
            background-color: var(--surface-color);
        }

        .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            margin-bottom: 1rem;
        }

        .card-content {
            padding: 16px;
        }

        .btn-primary {
            background-color: var(--primary-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-accent {
            background-color: var(--accent-color);
        }

        .btn-warn {
            background-color: var(--warn-color);
        }

        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .page-header {
            padding: 1rem 0;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(0,0,0,.1);
        }

        .page-title {
            font-weight: 500;
            margin: 0;
        }

        .breadcrumb {
            font-size: 1rem;
        }

        .breadcrumb:before {
            color: var(--text-secondary);
        }

        .breadcrumb:last-child {
            color: var(--text-primary);
        }

        .table-responsive {
            overflow-x: auto;
        }

        .data-table th {
            font-weight: 500;
            color: var(--text-primary);
        }

        .data-table td {
            color: var(--text-secondary);
        }

        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-primary {
            color: #fff;
            background-color: var(--primary-color);
        }

        .badge-secondary {
            color: #fff;
            background-color: var(--secondary-color);
        }

        .badge-accent {
            color: #fff;
            background-color: var(--accent-color);
        }

        .badge-warn {
            color: #fff;
            background-color: var(--warn-color);
        }

        .badge-success {
            color: #fff;
            background-color: #4CAF50;
        }

        .badge-danger {
            color: #fff;
            background-color: #F44336;
        }

        .badge-warning {
            color: #fff;
            background-color: #FF9800;
        }

        .loader {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .toast {
            border-radius: 4px;
        }

        .footer {
            padding: 1rem 0;
            background-color: var(--surface-color);
            border-top: 1px solid rgba(0,0,0,.1);
        }

        .footer-links a {
            color: var(--text-secondary);
            margin-right: 1rem;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-wrapper">
            <a href="/" class="brand-logo">
                <i class="material-icons left" style="margin-left: 10px;">security</i>
                Phish X
            </a>
            <a href="#" data-target="mobile-nav" class="sidenav-trigger">
                <i class="material-icons">menu</i>
            </a>
            <ul class="right hide-on-med-and-down">
                <li><a href="/tasks">任务管理</a></li>
                <li><a href="/plugin-management">插件管理</a></li>
                <li><a href="/email-test">邮件测试</a></li>
                <li><a href="/scheduler">任务调度</a></li>
                <li><a href="/dashboard">数据展示</a></li>
                <li><a href="/agent-management">Agent管理</a></li>
                <li id="authSection">
                    <!-- Auth buttons will be inserted here by JavaScript -->
                </li>
            </ul>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <ul class="sidenav" id="mobile-nav">
        <li><a href="/tasks">任务管理</a></li>
        <li><a href="/plugin-management">插件管理</a></li>
        <li><a href="/email-test">邮件测试</a></li>
        <li><a href="/scheduler">任务调度</a></li>
        <li><a href="/dashboard">数据展示</a></li>
        <li><a href="/agent-management">Agent管理</a></li>
        <li id="mobileAuthSection">
            <!-- Auth buttons will be inserted here by JavaScript -->
        </li>
    </ul>

    <!-- Breadcrumbs -->
    <nav class="breadcrumb-container">
        <div class="nav-wrapper">
            <div class="col s12">
                <a href="/" class="breadcrumb">首页</a>
                <a href="#!" class="breadcrumb">概览</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h4 class="page-title">欢迎使用 Phish X</h4>
                <p class="grey-text">这是一个轻便型、插件化、可扩展的社会工程安全测试平台。</p>
            </div>

            <!-- Feature Cards -->
            <div class="row">
                <!-- Agent管理模块 -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <div class="feature-icon">
                                <i class="material-icons">smart_toy</i>
                            </div>
                            <h5 class="card-title">Agent管理</h5>
                            <p class="grey-text">管理邮件发送Agent，包括注册、状态监控和任务分发。</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="/agent-management" class="btn btn-primary">进入</a>
                        </div>
                    </div>
                </div>

                <!-- 邮件插件模块 -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <div class="feature-icon">
                                <i class="material-icons">extension</i>
                            </div>
                            <h5 class="card-title">邮件插件</h5>
                            <p class="grey-text">管理邮件漏洞插件，配置插件参数和效果预览。</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="/plugin-management" class="btn btn-secondary">进入</a>
                        </div>
                    </div>
                </div>

                <!-- 任务编排模块 -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <div class="feature-icon">
                                <i class="material-icons">assignment</i>
                            </div>
                            <h5 class="card-title">任务编排</h5>
                            <p class="grey-text">创建和管理钓鱼测试任务，包括目标列表和执行调度。</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="/tasks" class="btn btn-accent">进入</a>
                        </div>
                    </div>
                </div>

                <!-- 结果跟踪模块 -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <div class="feature-icon">
                                <i class="material-icons">track_changes</i>
                            </div>
                            <h5 class="card-title">结果跟踪</h5>
                            <p class="grey-text">跟踪邮件送达和用户行为，进行数据统计分析。</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="/dashboard" class="btn btn-warn">进入</a>
                        </div>
                    </div>
                </div>

                <!-- 任务调度模块 -->
                <div class="col s12 m6 l4">
                    <div class="card feature-card">
                        <div class="card-content center-align">
                            <div class="feature-icon">
                                <i class="material-icons">schedule</i>
                            </div>
                            <h5 class="card-title">任务调度</h5>
                            <p class="grey-text">调度和管理定时任务，控制任务执行时间。</p>
                        </div>
                        <div class="card-action center-align">
                            <a href="/scheduler" class="btn grey darken-1">进入</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="page-footer">
        <div class="container">
            <div class="row">
                <div class="col l6 s12">
                    <h5 class="white-text">Phish X</h5>
                    <p class="grey-text text-lighten-4">一个轻便型、插件化、可扩展的社会工程安全测试平台。</p>
                </div>
                <div class="col l4 offset-l2 s12">
                    <h5 class="white-text">链接</h5>
                    <ul class="footer-links">
                        <li><a class="grey-text text-lighten-3" href="/">首页</a></li>
                        <li><a class="grey-text text-lighten-3" href="/docs">文档</a></li>
                        <li><a class="grey-text text-lighten-3" href="/support">支持</a></li>
                        <li><a class="grey-text text-lighten-3" href="/about">关于</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-copyright">
            <div class="container">
                © 2023 Phish X. 保留所有权利。
                <a class="grey-text text-lighten-4 right" href="#!">隐私政策</a>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Materialize components
            $('.sidenav').sidenav();
            $('.collapsible').collapsible();
            $('.tabs').tabs();
            $('.modal').modal();
            $('.tooltipped').tooltip();
            $('select').formSelect();
            
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (token) {
                // User is logged in, show user info and logout button
                $('#authSection').html(`
                    <li>
                        <a href="#" id="logoutBtn" class="waves-effect waves-light btn red lighten-1">
                            <i class="material-icons left">exit_to_app</i>退出登录
                        </a>
                    </li>
                `);
                
                $('#mobileAuthSection').html(`
                    <li>
                        <a href="#" id="mobileLogoutBtn">
                            <i class="material-icons left">exit_to_app</i>退出登录
                        </a>
                    </li>
                `);
                
                $('#logoutBtn, #mobileLogoutBtn').click(function(e) {
                    e.preventDefault();
                    localStorage.removeItem('token');
                    location.reload();
                });
            } else {
                // User is not logged in, show login and register buttons
                $('#authSection').html(`
                    <li>
                        <a href="/login" class="waves-effect waves-light btn blue lighten-1">
                            <i class="material-icons left">account_circle</i>登录
                        </a>
                    </li>
                    <li>
                        <a href="/register" class="waves-effect waves-light btn green lighten-1">
                            <i class="material-icons left">person_add</i>注册
                        </a>
                    </li>
                `);
                
                $('#mobileAuthSection').html(`
                    <li><a href="/login"><i class="material-icons left">account_circle</i>登录</a></li>
                    <li><a href="/register"><i class="material-icons left">person_add</i>注册</a></li>
                `);
            }
        });
    </script>
</body>
</html>