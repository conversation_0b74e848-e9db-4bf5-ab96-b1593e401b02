package main

import (
	"testing"

	"aiphish/internal/auth"
)

func TestJWTToken(t *testing.T) {
	// Test token generation
	tokenString, err := auth.GenerateToken("testuser", "<EMAIL>")
	if err != nil {
		t.<PERSON>rrorf("Failed to generate token: %v", err)
	}

	// Test token parsing
	claims, err := auth.ParseToken(tokenString)
	if err != nil {
		t.<PERSON>rf("Failed to parse token: %v", err)
	}

	// Check claims
	if claims.Username != "testuser" {
		t.<PERSON><PERSON><PERSON>("Expected username testuser, got %s", claims.Username)
	}

	if claims.Email != "<EMAIL>" {
		t.<PERSON>rf("<NAME_EMAIL>, got %s", claims.Email)
	}

	// Check token expiration
	if claims.ExpiresAt.Unix() <= 0 {
		t.<PERSON><PERSON>rf("Expected expiration time to be set")
	}

	// Test invalid token
	_, err = auth.ParseToken("invalid.token.string")
	if err == nil {
		t.Errorf("Expected error when parsing invalid token")
	}
}