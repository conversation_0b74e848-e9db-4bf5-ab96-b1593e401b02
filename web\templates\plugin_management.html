<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件管理 - Phish X</title>
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Materialize CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --accent-color: #FF9800;
            --warn-color: #F44336;
            --background-color: #f5f5f5;
            --surface-color: #ffffff;
            --text-primary: rgba(0, 0, 0, 0.87);
            --text-secondary: rgba(0, 0, 0, 0.54);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .sidenav {
            background-color: var(--surface-color);
        }

        .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            margin-bottom: 1rem;
        }

        .card-content {
            padding: 16px;
        }

        .btn-primary {
            background-color: var(--primary-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-accent {
            background-color: var(--accent-color);
        }

        .btn-warn {
            background-color: var(--warn-color);
        }

        .page-header {
            padding: 1rem 0;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(0,0,0,.1);
        }

        .page-title {
            font-weight: 500;
            margin: 0;
        }

        .breadcrumb {
            font-size: 1rem;
        }

        .breadcrumb:before {
            color: var(--text-secondary);
        }

        .breadcrumb:last-child {
            color: var(--text-primary);
        }

        .table-responsive {
            overflow-x: auto;
        }

        .data-table th {
            font-weight: 500;
            color: var(--text-primary);
        }

        .data-table td {
            color: var(--text-secondary);
        }

        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-primary {
            color: #fff;
            background-color: var(--primary-color);
        }

        .badge-secondary {
            color: #fff;
            background-color: var(--secondary-color);
        }

        .badge-accent {
            color: #fff;
            background-color: var(--accent-color);
        }

        .badge-warn {
            color: #fff;
            background-color: var(--warn-color);
        }

        .badge-success {
            color: #fff;
            background-color: #4CAF50;
        }

        .badge-danger {
            color: #fff;
            background-color: #F44336;
        }

        .badge-warning {
            color: #fff;
            background-color: #FF9800;
        }

        .loader {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .toast {
            border-radius: 4px;
        }

        .footer {
            padding: 1rem 0;
            background-color: var(--surface-color);
            border-top: 1px solid rgba(0,0,0,.1);
        }

        .footer-links a {
            color: var(--text-secondary);
            margin-right: 1rem;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }
        
        .plugin-info {
            background-color: #e3f2fd;
            border-left: 4px solid var(--primary-color);
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-wrapper">
            <a href="/" class="brand-logo">
                <i class="material-icons left" style="margin-left: 10px;">security</i>
                Phish X
            </a>
            <a href="#" data-target="mobile-nav" class="sidenav-trigger">
                <i class="material-icons">menu</i>
            </a>
            <ul class="right hide-on-med-and-down">
                <li><a href="/tasks">任务管理</a></li>
                <li class="active"><a href="/plugin-management">插件管理</a></li>
                <li><a href="/email-test">邮件测试</a></li>
                <li><a href="/scheduler">任务调度</a></li>
                <li><a href="/dashboard">数据展示</a></li>
                <li><a href="/agent-management">Agent管理</a></li>
                <li id="authSection">
                    <!-- Auth buttons will be inserted here by JavaScript -->
                </li>
            </ul>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <ul class="sidenav" id="mobile-nav">
        <li><a href="/tasks">任务管理</a></li>
        <li class="active"><a href="/plugin-management">插件管理</a></li>
        <li><a href="/email-test">邮件测试</a></li>
        <li><a href="/scheduler">任务调度</a></li>
        <li><a href="/dashboard">数据展示</a></li>
        <li><a href="/agent-management">Agent管理</a></li>
        <li id="mobileAuthSection">
            <!-- Auth buttons will be inserted here by JavaScript -->
        </li>
    </ul>

    <!-- Breadcrumbs -->
    <nav class="breadcrumb-container">
        <div class="nav-wrapper">
            <div class="col s12">
                <a href="/" class="breadcrumb">首页</a>
                <a href="/plugin-management" class="breadcrumb">插件管理</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="page-header">
                        <h2 class="page-title">插件管理</h2>
                        <p class="lead">管理邮件漏洞插件，配置插件参数和效果预览。</p>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">可用插件</h5>
                            <button class="btn btn-sm btn-outline-primary" id="refreshPlugins">
                                <i class="material-icons left">refresh</i> 刷新
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>描述</th>
                                            <th>版本</th>
                                            <th>类型</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="pluginsTableBody">
                                        <!-- Plugin数据将通过JavaScript动态加载 -->
                                        <tr>
                                            <td colspan="5" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">插件详情</h5>
                        </div>
                        <div class="card-body">
                            <div id="pluginDetail">
                                <p class="text-center">请选择一个插件以查看详细信息。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="page-footer">
        <div class="container">
            <div class="row">
                <div class="col l6 s12">
                    <h5 class="white-text">Phish X</h5>
                    <p class="grey-text text-lighten-4">一个轻便型、插件化、可扩展的社会工程安全测试平台。</p>
                </div>
                <div class="col l4 offset-l2 s12">
                    <h5 class="white-text">链接</h5>
                    <ul class="footer-links">
                        <li><a class="grey-text text-lighten-3" href="/">首页</a></li>
                        <li><a class="grey-text text-lighten-3" href="/docs">文档</a></li>
                        <li><a class="grey-text text-lighten-3" href="/support">支持</a></li>
                        <li><a class="grey-text text-lighten-3" href="/about">关于</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-copyright">
            <div class="container">
                © 2023 Phish X. 保留所有权利。
                <a class="grey-text text-lighten-4 right" href="#!">隐私政策</a>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Materialize components
            $('.sidenav').sidenav();
            $('.modal').modal();
            $('.collapsible').collapsible();
            $('.tabs').tabs();
            $('.tooltipped').tooltip();
            $('select').formSelect();
            
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (!token) {
                // Redirect to login page if not logged in
                window.location.href = '/login';
                return;
            }
            
            // Show user info and logout button
            $('#authSection').html(`
                <li class="nav-item">
                    <a class="nav-link waves-effect waves-light btn red lighten-1" href="#" id="logoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#mobileAuthSection').html(`
                <li>
                    <a href="#" id="mobileLogoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#logoutBtn, #mobileLogoutBtn').click(function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                window.location.href = '/login';
            });
            
            // Load plugins
            loadPlugins();
            
            // Refresh plugins when button is clicked
            $('#refreshPlugins').click(function() {
                loadPlugins();
            });
        });
        
        function loadPlugins() {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: '/api/v1/plugins/list',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(result) {
                    // Clear table
                    $('#pluginsTableBody').empty();
                    
                    // Check if there are plugins
                    if (!result.plugins || result.plugins.length === 0) {
                        $('#pluginsTableBody').html('<tr><td colspan="5" class="text-center">暂无可用插件</td></tr>');
                        return;
                    }
                    
                    // Add plugins to table
                    result.plugins.forEach(pluginName => {
                        // Get plugin info
                        $.ajax({
                            url: `/api/v1/plugins/${encodeURIComponent(pluginName)}/info`,
                            method: 'GET',
                            headers: {
                                'Authorization': 'Bearer ' + token
                            },
                            success: function(info) {
                                $('#pluginsTableBody').append(`
                                    <tr>
                                        <td>${info.name}</td>
                                        <td>${info.description}</td>
                                        <td>${info.version}</td>
                                        <td>${info.type || '未知'}</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary waves-effect waves-light" onclick="viewPlugin('${info.name}')">
                                                <i class="material-icons">visibility</i>
                                            </button>
                                        </td>
                                    </tr>
                                `);
                            },
                            error: function(xhr, status, error) {
                                console.log('获取插件信息失败: ' + pluginName);
                                // Still add the plugin to the table with minimal info
                                $('#pluginsTableBody').append(`
                                    <tr>
                                        <td>${pluginName}</td>
                                        <td>未知</td>
                                        <td>未知</td>
                                        <td>未知</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary waves-effect waves-light" onclick="viewPlugin('${pluginName}')">
                                                <i class="material-icons">visibility</i>
                                            </button>
                                        </td>
                                    </tr>
                                `);
                            }
                        });
                    });
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        $('#pluginsTableBody').html('<tr><td colspan="5" class="text-center">加载插件列表失败</td></tr>');
                        M.toast({html: '加载插件列表失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                        console.log('加载插件列表失败: ' + xhr.responseJSON.error);
                    }
                }
            });
        }
        
        function viewPlugin(pluginName) {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: `/api/v1/plugins/${encodeURIComponent(pluginName)}/info`,
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(info) {
                    let detailHtml = `
                        <h5>${info.name}</h5>
                        <p><strong>描述:</strong> ${info.description}</p>
                        <p><strong>版本:</strong> ${info.version}</p>
                        <p><strong>类型:</strong> ${info.type || '未知'}</p>
                    `;
                    
                    // Add a section to test the plugin
                    detailHtml += `
                        <div class="plugin-info">
                            <h6>测试插件</h6>
                            <form id="testPluginForm">
                                <div class="input-field">
                                    <input type="text" id="testTarget" placeholder="目标 (例如: example.com)">
                                    <label for="testTarget">目标</label>
                                </div>
                                <div class="input-field">
                                    <textarea id="testParams" class="materialize-textarea" placeholder='{"key": "value"}'></textarea>
                                    <label for="testParams">参数 (JSON格式)</label>
                                </div>
                                <button class="btn waves-effect waves-light" type="button" onclick="testPlugin('${info.name}')">运行插件</button>
                            </form>
                            <div id="testResult" class="mt-3" style="display:none;">
                                <h6>运行结果:</h6>
                                <pre id="testResultContent"></pre>
                            </div>
                        </div>
                    `;
                    
                    $('#pluginDetail').html(detailHtml);
                    
                    // Re-initialize Materialize components for the new form
                    $('#testParams').characterCounter();
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: '获取插件详情失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
        }
        
        function testPlugin(pluginName) {
            const token = localStorage.getItem('token');
            const target = $('#testTarget').val();
            let params = $('#testParams').val();
            
            // Parse params as JSON if provided
            if (params) {
                try {
                    params = JSON.parse(params);
                } catch (e) {
                    M.toast({html: '参数必须是有效的JSON格式', classes: 'rounded red'});
                    return;
                }
            } else {
                params = {};
            }
            
            const testData = {
                target: target,
                params: params
            };
            
            $.ajax({
                url: `/api/v1/plugins/${encodeURIComponent(pluginName)}/run`,
                method: 'POST',
                contentType: 'application/json',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                data: JSON.stringify(testData),
                success: function(result) {
                    $('#testResultContent').text(JSON.stringify(result, null, 2));
                    $('#testResult').show();
                    M.toast({html: '插件运行成功', classes: 'rounded green'});
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        $('#testResultContent').text(xhr.responseJSON ? JSON.stringify(xhr.responseJSON, null, 2) : '未知错误');
                        $('#testResult').show();
                        M.toast({html: '插件运行失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误'), classes: 'rounded red'});
                    }
                }
            });
        }
    </script>
</body>
</html>