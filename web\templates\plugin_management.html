<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>插件管理 - AI Phish</title>\n    <link rel=\"stylesheet\" href=\"https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css\">\n</head>\n<body>\n    <nav class=\"navbar navbar-expand-lg navbar-light bg-light\">\n        <a class=\"navbar-brand\" href=\"/\">AI Phish</a>\n        <div class=\"collapse navbar-collapse\">\n            <ul class=\"navbar-nav mr-auto\">\n                <li class=\"nav-item\">\n                    <a class=\"nav-link\" href=\"/tasks\">任务管理</a>\n                </li>\n                <li class=\"nav-item\">\n                    <a class=\"nav-link\" href=\"/plugins\">插件管理</a>\n                </li>\n                <li class=\"nav-item\">\n                    <a class=\"nav-link\" href=\"/email-test\">邮件测试</a>\n                </li>\n            </ul>\n            <ul class=\"navbar-nav ml-auto\" id=\"authSection\">\n                <!-- Auth buttons will be inserted here by JavaScript -->\n            </ul>\n        </div>\n    </nav>\n\n    <div class=\"container mt-4\">\n        <h2>插件管理</h2>\n        \n        <!-- Plugin List -->\n        <div class=\"card mb-4\">\n            <div class=\"card-header\">\n                <h5 class=\"mb-0\">可用插件</h5>\n            </div>\n            <div class=\"card-body\">\n                <div id=\"pluginList\">\n                    <p>加载中...</p>\n                </div>\n            </div>\n        </div>\n        \n        <!-- Run Plugin -->\n        <div class=\"card\">\n            <div class=\"card-header\">\n                <h5 class=\"mb-0\">运行插件</h5>\n            </div>\n            <div class=\"card-body\">\n                <form id=\"runPluginForm\">\n                    <div class=\"form-group\">\n                        <label for=\"pluginName\">插件名称</label>\n                        <input type=\"text\" class=\"form-control\" id=\"pluginName\" required>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"pluginTarget\">目标</label>\n                        <input type=\"text\" class=\"form-control\" id=\"pluginTarget\" required>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"pluginParams\">参数 (JSON格式)</label>\n                        <textarea class=\"form-control\" id=\"pluginParams\" rows=\"3\"></textarea>\n                    </div>\n                    <button type=\"submit\" class=\"btn btn-primary\">运行插件</button>\n                </form>\n                <div id=\"pluginResult\" class=\"mt-3\"></div>\n            </div>\n        </div>\n    </div>\n\n    <script src=\"https://code.jquery.com/jquery-3.5.1.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js\"></script>\n    <script src=\"https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js\"></script>\n    <script>\n        $(document).ready(function() {\n            // Check if user is logged in\n            const token = localStorage.getItem('token');\n            \n            if (!token) {\n                // Redirect to login page if not logged in\n                window.location.href = '/login';\n                return;\n            }\n            \n            // Show user info and logout button\n            $('#authSection').html(`\n                <li class=\"nav-item\">\n                    <a class=\"nav-link\" href=\"#\" id=\"logoutBtn\">退出登录</a>\n                </li>\n            `);\n            \n            $('#logoutBtn').click(function(e) {\n                e.preventDefault();\n                localStorage.removeItem('token');\n                window.location.href = '/login';\n            });\n            \n            // Load plugin list\n            loadPluginList();\n            \n            // Run plugin form submission\n            $('#runPluginForm').submit(function(e) {\n                e.preventDefault();\n                \n                const pluginName = $('#pluginName').val();\n                const target = $('#pluginTarget').val();\n                const paramsStr = $('#pluginParams').val();\n                \n                let params = {};\n                if (paramsStr) {\n                    try {\n                        params = JSON.parse(paramsStr);\n                    } catch (e) {\n                        alert('参数格式不正确，请输入有效的JSON');\n                        return;\n                    }\n                }\n                \n                runPlugin(pluginName, target, params);\n            });\n        });\n        \n        function loadPluginList() {\n            const token = localStorage.getItem('token');\n            \n            $.ajax({\n                url: '/api/v1/plugins/list',\n                method: 'GET',\n                headers: {\n                    'Authorization': token\n                },\n                success: function(result) {\n                    let pluginListHtml = '<ul class=\"list-group\">';\n                    result.plugins.forEach(pluginName => {\n                        pluginListHtml += `\n                            <li class=\"list-group-item d-flex justify-content-between align-items-center\">\n                                ${pluginName}\n                                <button class=\"btn btn-sm btn-info\" onclick=\"getPluginInfo('${pluginName}')\">查看信息</button>\n                            </li>\n                        `;\n                    });\n                    pluginListHtml += '</ul>';\n                    $('#pluginList').html(pluginListHtml);\n                },\n                error: function(xhr, status, error) {\n                    if (xhr.status === 401) {\n                        alert('登录已过期，请重新登录');\n                        localStorage.removeItem('token');\n                        window.location.href = '/login';\n                    } else {\n                        $('#pluginList').html('<p class=\"text-danger\">加载插件列表失败: ' + xhr.responseJSON.error + '</p>');\n                    }\n                }\n            });\n        }\n        \n        function getPluginInfo(pluginName) {\n            const token = localStorage.getItem('token');\n            \n            $.ajax({\n                url: `/api/v1/plugins/${pluginName}/info`,\n                method: 'GET',\n                headers: {\n                    'Authorization': token\n                },\n                success: function(result) {\n                    alert(`插件名称: ${result.name}\\n描述: ${result.description}\\n版本: ${result.version}`);\n                },\n                error: function(xhr, status, error) {\n                    if (xhr.status === 401) {\n                        alert('登录已过期，请重新登录');\n                        localStorage.removeItem('token');\n                        window.location.href = '/login';\n                    } else {\n                        alert('获取插件信息失败: ' + xhr.responseJSON.error);\n                    }\n                }\n            });\n        }\n        \n        function runPlugin(pluginName, target, params) {\n            const token = localStorage.getItem('token');\n            \n            $('#pluginResult').html('<div class=\"alert alert-info\">正在运行插件...</div>');\n            \n            $.ajax({\n                url: `/api/v1/plugins/${pluginName}/run`,\n                method: 'POST',\n                headers: {\n                    'Authorization': token\n                },\n                contentType: 'application/json',\n                data: JSON.stringify({\n                    target: target,\n                    params: params\n                }),\n                success: function(result) {\n                    $('#pluginResult').html(`\n                        <div class=\"alert alert-success\">\n                            <h6>插件运行结果:</h6>\n                            <p>状态: ${result.status}</p>\n                            <p>详情: ${result.detail}</p>\n                        </div>\n                    `);\n                },\n                error: function(xhr, status, error) {\n                    if (xhr.status === 401) {\n                        alert('登录已过期，请重新登录');\n                        localStorage.removeItem('token');\n                        window.location.href = '/login';\n                    } else {\n                        $('#pluginResult').html(`\n                            <div class=\"alert alert-danger\">\n                                <h6>插件运行失败:</h6>\n                                <p>${xhr.responseJSON.error}</p>\n                            </div>\n                        `);\n                    }\n                }\n            });\n        }\n    </script>\n</body>\n</html>