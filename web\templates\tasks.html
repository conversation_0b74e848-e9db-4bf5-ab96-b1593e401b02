<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理 - Phish X</title>
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Materialize CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --accent-color: #FF9800;
            --warn-color: #F44336;
            --background-color: #f5f5f5;
            --surface-color: #ffffff;
            --text-primary: rgba(0, 0, 0, 0.87);
            --text-secondary: rgba(0, 0, 0, 0.54);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .sidenav {
            background-color: var(--surface-color);
        }

        .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            margin-bottom: 1rem;
        }

        .card-content {
            padding: 16px;
        }

        .btn-primary {
            background-color: var(--primary-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-accent {
            background-color: var(--accent-color);
        }

        .btn-warn {
            background-color: var(--warn-color);
        }

        .page-header {
            padding: 1rem 0;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(0,0,0,.1);
        }

        .page-title {
            font-weight: 500;
            margin: 0;
        }

        .breadcrumb {
            font-size: 1rem;
        }

        .breadcrumb:before {
            color: var(--text-secondary);
        }

        .breadcrumb:last-child {
            color: var(--text-primary);
        }

        .table-responsive {
            overflow-x: auto;
        }

        .data-table th {
            font-weight: 500;
            color: var(--text-primary);
        }

        .data-table td {
            color: var(--text-secondary);
        }

        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-primary {
            color: #fff;
            background-color: var(--primary-color);
        }

        .badge-secondary {
            color: #fff;
            background-color: var(--secondary-color);
        }

        .badge-accent {
            color: #fff;
            background-color: var(--accent-color);
        }

        .badge-warn {
            color: #fff;
            background-color: var(--warn-color);
        }

        .badge-success {
            color: #fff;
            background-color: #4CAF50;
        }

        .badge-danger {
            color: #fff;
            background-color: #F44336;
        }

        .badge-warning {
            color: #fff;
            background-color: #FF9800;
        }

        .loader {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .toast {
            border-radius: 4px;
        }

        .footer {
            padding: 1rem 0;
            background-color: var(--surface-color);
            border-top: 1px solid rgba(0,0,0,.1);
        }

        .footer-links a {
            color: var(--text-secondary);
            margin-right: 1rem;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }
        
        .chips {
            border: none;
            box-shadow: none;
            padding: 0;
            margin: 0 0 8px 0;
        }
        
        .chips .input {
            margin: 0;
        }
        
        .chips .chip {
            margin: 4px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-wrapper">
            <a href="/" class="brand-logo">
                <i class="material-icons left" style="margin-left: 10px;">security</i>
                Phish X
            </a>
            <a href="#" data-target="mobile-nav" class="sidenav-trigger">
                <i class="material-icons">menu</i>
            </a>
            <ul class="right hide-on-med-and-down">
                <li class="active"><a href="/tasks">任务管理</a></li>
                <li><a href="/plugin-management">插件管理</a></li>
                <li><a href="/email-test">邮件测试</a></li>
                <li><a href="/scheduler">任务调度</a></li>
                <li><a href="/dashboard">数据展示</a></li>
                <li><a href="/agent-management">Agent管理</a></li>
                <li id="authSection">
                    <!-- Auth buttons will be inserted here by JavaScript -->
                </li>
            </ul>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <ul class="sidenav" id="mobile-nav">
        <li class="active"><a href="/tasks">任务管理</a></li>
        <li><a href="/plugin-management">插件管理</a></li>
        <li><a href="/email-test">邮件测试</a></li>
        <li><a href="/scheduler">任务调度</a></li>
        <li><a href="/dashboard">数据展示</a></li>
        <li><a href="/agent-management">Agent管理</a></li>
        <li id="mobileAuthSection">
            <!-- Auth buttons will be inserted here by JavaScript -->
        </li>
    </ul>

    <!-- Breadcrumbs -->
    <nav class="breadcrumb-container">
        <div class="nav-wrapper">
            <div class="col s12">
                <a href="/" class="breadcrumb">首页</a>
                <a href="/tasks" class="breadcrumb">任务管理</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="page-header">
                        <h2 class="page-title">任务管理</h2>
                        <p class="lead">创建和管理钓鱼测试任务，包括目标列表和执行调度。</p>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">现有任务</h5>
                            <button class="btn btn-sm btn-outline-primary" id="refreshTasks">
                                <i class="material-icons left">refresh</i> 刷新
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>描述</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tasksTableBody">
                                        <!-- Task数据将通过JavaScript动态加载 -->
                                        <tr>
                                            <td colspan="5" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">创建新任务</h5>
                        </div>
                        <div class="card-body">
                            <form id="createTaskForm">
                                <div class="form-group">
                                    <label for="taskName">任务名称</label>
                                    <input type="text" class="form-control" id="taskName" required>
                                </div>
                                <div class="form-group">
                                    <label for="taskDescription">任务描述</label>
                                    <textarea class="materialize-textarea" id="taskDescription"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label>选择插件 (可选)</label>
                                    <div id="pluginsCheckboxes">
                                        <!-- 插件复选框将通过JavaScript动态加载 -->
                                        <p>加载中...</p>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="taskTargets">目标邮箱 (每行一个)</label>
                                    <textarea class="materialize-textarea" id="taskTargets" required></textarea>
                                </div>
                                
                                <!-- Initial Email Section -->
                                <div class="section">
                                    <h6>初始邮件信息</h6>
                                    <div class="form-group">
                                        <label for="initialEmailFrom">发件人</label>
                                        <input type="text" class="form-control" id="initialEmailFrom">
                                    </div>
                                    <div class="form-group">
                                        <label for="initialEmailSubject">邮件主题</label>
                                        <input type="text" class="form-control" id="initialEmailSubject">
                                    </div>
                                    <div class="form-group">
                                        <label for="initialEmailBody">邮件正文</label>
                                        <textarea class="materialize-textarea" id="initialEmailBody"></textarea>
                                    </div>
                                </div>
                                
                                <!-- SMTP Configuration Section -->
                                <div class="section">
                                    <h6>SMTP配置</h6>
                                    <div class="form-group">
                                        <label for="smtpHost">SMTP主机</label>
                                        <input type="text" class="form-control" id="smtpHost">
                                    </div>
                                    <div class="form-group">
                                        <label for="smtpPort">SMTP端口</label>
                                        <input type="text" class="form-control" id="smtpPort">
                                    </div>
                                    <div class="form-group">
                                        <label for="smtpUsername">用户名</label>
                                        <input type="text" class="form-control" id="smtpUsername">
                                    </div>
                                    <div class="form-group">
                                        <label for="smtpPassword">密码</label>
                                        <input type="password" class="form-control" id="smtpPassword">
                                    </div>
                                </div>
                                
                                <!-- Tracking ID Section -->
                                <div class="section">
                                    <h6>跟踪ID</h6>
                                    <div class="form-group">
                                        <label for="trackingId">跟踪ID</label>
                                        <input type="text" class="form-control" id="trackingId">
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-success">
                                    <i class="material-icons left">add</i> 创建任务
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="page-footer">
        <div class="container">
            <div class="row">
                <div class="col l6 s12">
                    <h5 class="white-text">Phish X</h5>
                    <p class="grey-text text-lighten-4">一个轻便型、插件化、可扩展的社会工程安全测试平台。</p>
                </div>
                <div class="col l4 offset-l2 s12">
                    <h5 class="white-text">链接</h5>
                    <ul class="footer-links">
                        <li><a class="grey-text text-lighten-3" href="/">首页</a></li>
                        <li><a class="grey-text text-lighten-3" href="/docs">文档</a></li>
                        <li><a class="grey-text text-lighten-3" href="/support">支持</a></li>
                        <li><a class="grey-text text-lighten-3" href="/about">关于</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-copyright">
            <div class="container">
                © 2023 Phish X. 保留所有权利。
                <a class="grey-text text-lighten-4 right" href="#!">隐私政策</a>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Materialize components
            $('.sidenav').sidenav();
            $('.modal').modal();
            $('.collapsible').collapsible();
            $('.tabs').tabs();
            $('.tooltipped').tooltip();
            $('select').formSelect();
            $('.materialize-textarea').characterCounter();
            
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (!token) {
                // Redirect to login page if not logged in
                window.location.href = '/login';
                return;
            }
            
            // Show user info and logout button
            $('#authSection').html(`
                <li class="nav-item">
                    <a class="nav-link waves-effect waves-light btn red lighten-1" href="#" id="logoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#mobileAuthSection').html(`
                <li>
                    <a href="#" id="mobileLogoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#logoutBtn, #mobileLogoutBtn').click(function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                window.location.href = '/login';
            });
            
            // Load tasks and plugins
            loadTasks();
            loadPluginsForTaskCreation();
            
            // Refresh tasks when button is clicked
            $('#refreshTasks').click(function() {
                loadTasks();
            });
            
            // Handle form submission
            $('#createTaskForm').submit(function(e) {
                e.preventDefault();
                createTask();
            });
        });
        
        function loadTasks() {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: '/api/v1/tasks',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(tasks) {
                    // Clear table
                    $('#tasksTableBody').empty();
                    
                    // Check if there are tasks
                    if (!tasks || tasks.length === 0) {
                        $('#tasksTableBody').html('<tr><td colspan="5" class="text-center">暂无任务</td></tr>');
                        return;
                    }
                    
                    // Add tasks to table
                    tasks.forEach(task => {
                        const statusBadge = task.Status === 'completed' ? 
                            '<span class="badge badge-success">已完成</span>' : 
                            task.Status === 'pending' ? 
                            '<span class="badge badge-warning">待处理</span>' : 
                            '<span class="badge badge-danger">未知</span>';
                            
                        $('#tasksTableBody').append(`
                            <tr>
                                <td>${task.id}</td>
                                <td>${task.name}</td>
                                <td>${task.description || ''}</td>
                                <td>${statusBadge}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary waves-effect waves-light" onclick="runTask('${task.id}')">
                                        <i class="material-icons">play_arrow</i>
                                    </button>
                                    <button class="btn btn-sm btn-info waves-effect waves-light" onclick="viewTask('${task.id}')">
                                        <i class="material-icons">visibility</i>
                                    </button>
                                </td>
                            </tr>
                        `);
                    });
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        $('#tasksTableBody').html('<tr><td colspan="5" class="text-center">加载任务列表失败</td></tr>');
                        M.toast({html: '加载任务列表失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                        console.log('加载任务列表失败: ' + xhr.responseJSON.error);
                    }
                }
            });
        }
        
        function loadPluginsForTaskCreation() {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: '/api/v1/plugins/list',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(result) {
                    // Clear checkboxes container
                    $('#pluginsCheckboxes').empty();
                    
                    // Check if there are plugins
                    if (!result.plugins || result.plugins.length === 0) {
                        $('#pluginsCheckboxes').html('<p>暂无可用插件</p>');
                        return;
                    }
                    
                    // Create checkboxes for each plugin
                    let checkboxesHtml = '';
                    result.plugins.forEach(pluginName => {
                        checkboxesHtml += `
                            <div class="plugin-item">
                                <p>
                                    <label>
                                        <input type="checkbox" class="filled-in plugin-checkbox" id="plugin_${pluginName}" value="${pluginName}" />
                                        <span>${pluginName}</span>
                                    </label>
                                </p>
                                <div class="plugin-params" id="params_${pluginName}" style="display: none; margin-left: 20px;">
                                    <div class="form-group">
                                        <label>参数 (JSON格式):</label>
                                        <textarea class="materialize-textarea plugin-params-input" id="params_input_${pluginName}" placeholder='{"key": "value"}'></textarea>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    $('#pluginsCheckboxes').html(checkboxesHtml);
                    
                    // Add event listeners to show/hide parameter inputs
                    $('.plugin-checkbox').change(function() {
                        const pluginName = $(this).val();
                        if ($(this).is(':checked')) {
                            $(`#params_${pluginName}`).show();
                        } else {
                            $(`#params_${pluginName}`).hide();
                        }
                    });
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        $('#pluginsCheckboxes').html('<p>加载插件列表失败</p>');
                        M.toast({html: '加载插件列表失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                        console.log('加载插件列表失败: ' + xhr.responseJSON.error);
                    }
                }
            });
        }
        
        function createTask() {
            const token = localStorage.getItem('token');
            
            // Get form data
            const name = $('#taskName').val();
            const description = $('#taskDescription').val();
            
            // Get selected plugins
            const selectedPlugins = [];
            $('#pluginsCheckboxes input[type=checkbox].plugin-checkbox:checked').each(function() {
                const pluginName = $(this).val();
                const paramsInput = $(`#params_input_${pluginName}`).val().trim();
                let params = {};
                
                // Parse parameters if provided
                if (paramsInput) {
                    try {
                        params = JSON.parse(paramsInput);
                    } catch (e) {
                        M.toast({html: `插件 ${pluginName} 的参数格式不正确`, classes: 'rounded red'});
                        return;
                    }
                }
                
                selectedPlugins.push({
                    name: pluginName,
                    params: params
                });
            });
            
            // Get targets
            const targetsText = $('#taskTargets').val();
            const targets = targetsText.split('\n').filter(target => target.trim() !== '');
            
            // Validate required fields
            // Plugin selection is now optional
            // if (selectedPlugins.length === 0) {
            //     M.toast({html: '请至少选择一个插件', classes: 'rounded red'});
            //     return;
            // }
            
            if (targets.length === 0) {
                M.toast({html: '请至少提供一个目标邮箱', classes: 'rounded red'});
                return;
            }
            
            // Get initial email data
            const initialEmailFrom = $('#initialEmailFrom').val().trim();
            const initialEmailSubject = $('#initialEmailSubject').val().trim();
            const initialEmailBody = $('#initialEmailBody').val().trim();
            
            // Get SMTP configuration
            const smtpHost = $('#smtpHost').val().trim();
            const smtpPort = $('#smtpPort').val().trim();
            const smtpUsername = $('#smtpUsername').val().trim();
            const smtpPassword = $('#smtpPassword').val().trim();
            
            // Get tracking ID
            const trackingId = $('#trackingId').val().trim();
            
            // Prepare task data
            const taskData = {
                name: name,
                description: description,
                plugins: selectedPlugins,
                Targets: targets
            };
            
            // Add initial email data if provided
            if (initialEmailFrom || initialEmailSubject || initialEmailBody) {
                taskData.initial_email = {};
                if (initialEmailFrom) {
                    taskData.initial_email.From = initialEmailFrom;
                }
                if (initialEmailSubject) {
                    taskData.initial_email.Subject = initialEmailSubject;
                }
                if (initialEmailBody) {
                    taskData.initial_email.Body = initialEmailBody;
                }
                // 'To' field will be set by the backend for each target
                taskData.initial_email.To = [];
                taskData.initial_email.Headers = {};
                // TrackingID can be added if needed
                // taskData.initial_email.TrackingID = "";
            }
            
            // Add SMTP configuration if provided
            if (smtpHost && smtpPort) {
                taskData.smtp_config = {
                    Host: smtpHost,
                    Port: smtpPort,
                    Username: smtpUsername,
                    Password: smtpPassword
                };
            }
            
            // Add tracking ID if provided
            if (trackingId) {
                taskData.tracking_id = trackingId;
            }
            
            // Log the task data for debugging
            console.log('Sending task data:', taskData);
            
            $.ajax({
                url: '/api/v1/tasks',
                method: 'POST',
                contentType: 'application/json',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                data: JSON.stringify(taskData),
                success: function(result) {
                    M.toast({html: '任务创建成功', classes: 'rounded green'});
                    $('#createTaskForm')[0].reset();
                    // Uncheck all checkboxes
                    $('#pluginsCheckboxes input[type=checkbox]').prop('checked', false);
                    loadTasks();
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: '任务创建失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
        }
        
        function runTask(taskId) {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: `/api/v1/tasks/${encodeURIComponent(taskId)}/run`,
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(result) {
                    M.toast({html: '任务启动成功', classes: 'rounded green'});
                    loadTasks(); // Refresh task list to show updated status
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: '任务启动失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
        }
        
        function viewTask(taskId) {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: `/api/v1/tasks/${encodeURIComponent(taskId)}`,
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(task) {
                    // Create a modal to display task details
                    let modalHtml = `
                        <div id="taskModal" class="modal">
                            <div class="modal-content">
                                <h4>任务详情</h4>
                                <table class="striped">
                                    <tr><td><strong>ID:</strong></td><td>${task.id}</td></tr>
                                    <tr><td><strong>名称:</strong></td><td>${task.name}</td></tr>
                                    <tr><td><strong>描述:</strong></td><td>${task.description || ''}</td></tr>
                                    <tr><td><strong>状态:</strong></td><td>${task.status}</td></tr>
                                </table>
                            </div>
                            <div class="modal-footer">
                                <a href="#!" class="modal-close waves-effect waves-green btn-flat">关闭</a>
                            </div>
                        </div>
                    `;
                    
                    // Append modal to body and open it
                    $('body').append(modalHtml);
                    $('#taskModal').modal();
                    $('#taskModal').modal('open');
                    
                    // Remove modal from DOM when closed
                    $('#taskModal').on('modal-close', function() {
                        $('#taskModal').remove();
                    });
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: '获取任务详情失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
        }
    </script>
</body>
</html>