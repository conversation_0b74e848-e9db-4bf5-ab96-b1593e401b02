<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理 - AI Phish</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <a class="navbar-brand" href="/">AI Phish</a>
        <div class="collapse navbar-collapse">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/tasks">任务管理</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/plugins">插件管理</a>
                </li>
            </ul>
            <ul class="navbar-nav ml-auto" id="authSection">
                <!-- Auth buttons will be inserted here by JavaScript -->
            </ul>
        </div>
    </nav>

    <div class="container mt-4">
        <h2>任务管理</h2>
        <button class="btn btn-primary mb-3" data-toggle="modal" data-target="#createTaskModal">创建任务</button>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>描述</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody id="tasksTableBody">
                <!-- Tasks will be loaded here by JavaScript -->
            </tbody>
        </table>
    </div>

    <!-- Create Task Modal -->
    <div class="modal fade" id="createTaskModal" tabindex="-1" aria-labelledby="createTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createTaskModalLabel">创建任务</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="createTaskForm">
                        <div class="form-group">
                            <label for="taskName">名称</label>
                            <input type="text" class="form-control" id="taskName" required>
                        </div>
                        <div class="form-group">
                            <label for="taskDescription">描述</label>
                            <textarea class="form-control" id="taskDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitTask">创建</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (!token) {
                // Redirect to login page if not logged in
                window.location.href = '/login';
                return;
            }
            
            // Show user info and logout button
            $('#authSection').html(`
                <li class="nav-item">
                    <a class="nav-link" href="#" id="logoutBtn">退出登录</a>
                </li>
            `);
            
            $('#logoutBtn').click(function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                window.location.href = '/login';
            });
            
            // Load tasks
            loadTasks();
            
            // Submit task
            $('#submitTask').click(function() {
                const name = $('#taskName').val();
                const description = $('#taskDescription').val();
                const status = 'pending';

                $.ajax({
                    url: '/api/v1/tasks',
                    method: 'POST',
                    contentType: 'application/json',
                    headers: {
                        'Authorization': token
                    },
                    data: JSON.stringify({ name, description, status }),
                    success: function(result) {
                        $('#createTaskModal').modal('hide');
                        loadTasks();
                    },
                    error: function(xhr, status, error) {
                        alert('创建任务失败: ' + xhr.responseJSON.error);
                    }
                });
            });
        });
        
        function loadTasks() {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: '/api/v1/tasks',
                method: 'GET',
                headers: {
                    'Authorization': token
                },
                success: function(tasks) {
                    let tasksHtml = '';
                    tasks.forEach(task => {
                        tasksHtml += `
                            <tr>
                                <td>${task.id}</td>
                                <td>${task.name}</td>
                                <td>${task.description}</td>
                                <td>${task.status}</td>
                            </tr>
                        `;
                    });
                    $('#tasksTableBody').html(tasksHtml);
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        alert('登录已过期，请重新登录');
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        alert('加载任务失败: ' + xhr.responseJSON.error);
                    }
                }
            });
        }
    </script>
</body>
</html>