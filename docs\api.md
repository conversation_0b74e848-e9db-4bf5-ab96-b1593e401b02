# API 文档

## 认证

除登录和注册接口外，所有API接口都需要通过JWT Token进行认证。

在请求头中添加：
```
Authorization: Bearer <token>
```

## 用户认证

### 用户注册

```
POST /api/v1/register
```

**请求体**:
```json
{
  "username": "用户名",
  "email": "邮箱地址",
  "password": "密码"
}
```

**响应**:
```json
{
  "message": "User registered successfully"
}
```

### 用户登录

```
POST /api/v1/login
```

**请求体**:
```json
{
  "username": "用户名",
  "password": "密码"
}
```

**响应**:
```json
{
  "message": "Login successful",
  "token": "JWT Token"
}
```

## Agent管理

### 注册Agent

```
POST /api/v1/agents
```

**请求体**:
```json
{
  "id": "Agent ID",
  "api_endpoint": "Agent API端点地址"
}
```

**响应**:
```json
{
  "message": "Agent registered successfully"
}
```

### 获取Agent列表

```
GET /api/v1/agents
```

**响应**:
```json
{
  "agents": ["agent-id-1", "agent-id-2"]
}
```

### 获取Agent信息

```
GET /api/v1/agents/:id
```

**响应**:
```json
{
  "id": "agent-id",
  "version": "1.0.0",
  "status": "idle"
}
```

### 获取Agent状态

```
GET /api/v1/agents/:id/status
```

**响应**:
```json
{
  "id": "agent-id",
  "status": "idle|running|stopped|disconnected|error",
  "last_seen": "2023-01-01T00:00:00Z",
  "stats": {
    "total_sent": 100,
    "total_failed": 5,
    "total_queued": 10,
    "last_send_time": "2023-01-01T00:00:00Z",
    "avg_send_time": "0.5s",
    "success_rate": 95.0
  }
}
```

### 获取Agent统计信息

```
GET /api/v1/agents/:id/stats
```

**响应**:
```json
{
  "total_sent": 100,
  "total_failed": 5,
  "total_queued": 10,
  "last_send_time": "2023-01-01T00:00:00Z",
  "avg_send_time": "0.5s",
  "success_rate": 95.0
}
```

### 删除Agent

```
DELETE /api/v1/agents/:id
```

**响应**:
```json
{
  "message": "Agent deleted successfully"
}
```

## 任务管理

### 创建任务

```
POST /api/v1/tasks
```

**请求体**:
```json
{
  "name": "任务名称",
  "description": "任务描述",
  "plugins": [
    {
      "name": "插件名称",
      "params": {
        "参数名1": "参数值1",
        "参数名2": "参数值2"
      }
    }
  ],
  "targets": ["目标邮箱1", "目标邮箱2"],
  "initial_email": {
    "from": "发件人邮箱",
    "to": ["收件人邮箱"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {
      "头部字段": "值"
    }
  },
  "smtp_config": {
    "host": "SMTP服务器地址",
    "port": "SMTP服务器端口",
    "username": "用户名",
    "password": "密码"
  },
  "tracking_id": "跟踪ID"
}

可以通过在initial_email.headers中添加附件信息来传递附件：

```json
{ 
  "name": "Phishing Test with Attachment",
  "description": "Test email with SVG attachment",
  "targets": ["<EMAIL>"],
  "plugins": [],
  "initial_email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Chart",
    "body": "Please review the attached chart.",
    "headers": {
      "X-Email-Attachments": "[{\"filename\":\"chart.svg\",\"content\":\"PD94bWwgdmV...\",\"content_type\":\"image/svg+xml\"}]"
    }
  },
  "smtp_config": {
    "host": "smtp.example.com",
    "port": "587",
    "username": "<EMAIL>",
    "password": "password"
  }
}
```

**响应**:
```json
{
  "message": "Task created successfully",
  "id": "任务ID"
}
```

### 获取任务列表

```
GET /api/v1/tasks
```

**响应**:
```json
[
  {
    "ID": "任务ID",
    "Name": "任务名称",
    "Description": "任务描述",
    "Status": "任务状态"
  }
]
```

### 获取任务详情

```
GET /api/v1/tasks/:id
```

**响应**:
```json
{
  "ID": "任务ID",
  "Name": "任务名称",
  "Description": "任务描述",
  "Status": "任务状态"
}
```

### 执行任务

```
POST /api/v1/tasks/:id/run
```

**响应**:
```json
{
  "message": "Task executed successfully"
}
```

## 插件管理

### 获取插件列表

```
GET /api/v1/plugins/list
```

**响应**:
```json
{
  "plugins": ["插件1", "插件2"]
}
```

### 获取插件信息

```
GET /api/v1/plugins/:name/info
```

**响应**:
```json
{
  "name": "插件名称",
  "description": "插件描述",
  "version": "插件版本"
}
```

### 运行插件

```
POST /api/v1/plugins/:name/run
```

**请求体**:
```json
{
  "target": "目标邮箱",
  "email": {
    "from": "发件人邮箱",
    "to": ["收件人邮箱"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {
      "头部字段": "值"
    }
  },
  "params": {
    "参数名": "参数值"
  }
}
```

**响应**:
```json
{
  "status": "success|error",
  "email": {
    // 修改后的邮件数据（可选）
  },
  "detail": "详细信息"
}
```

## 任务调度

### 调度任务

```
POST /api/v1/scheduler/tasks/:id/schedule
```

**请求体**:
```json
{
  "interval": "执行间隔（秒）",
  "start_time": "开始时间（RFC3339格式，可选）",
  "end_time": "结束时间（RFC3339格式，可选）",
  "repeat_count": "重复次数（可选，默认1）"
}
```

**响应**:
```json
{
  "message": "Task scheduled successfully"
}
```

### 获取调度任务状态

```
GET /api/v1/scheduler/tasks/:id/status
```

**响应**:
```json
{
  "TaskID": "任务ID",
  "Interval": "执行间隔",
  "StartTime": "开始时间",
  "EndTime": "结束时间",
  "RepeatCount": "重复次数",
  "CurrentCount": "当前执行次数",
  "Status": "任务状态",
  "NextExecution": "下次执行时间",
  "LastExecution": "上次执行时间"
}
```

### 获取调度任务列表

```
GET /api/v1/scheduler/tasks
```

**响应**:
```json
[
  {
    "TaskID": "任务ID",
    "Interval": "执行间隔",
    "StartTime": "开始时间",
    "EndTime": "结束时间",
    "RepeatCount": "重复次数",
    "CurrentCount": "当前执行次数",
    "Status": "任务状态",
    "NextExecution": "下次执行时间",
    "LastExecution": "上次执行时间"
  }
]
```

## 数据展示

### 获取仪表板数据

```
GET /api/v1/dashboard/data
```

**响应**:
```json
{
  "tasks": [
    {
      "ID": "任务ID",
      "Name": "任务名称",
      "Description": "任务描述",
      "Status": "任务状态"
    }
  ],
  "plugins": [
    {
      "ID": "插件ID",
      "Name": "插件名称",
      "Version": "插件版本",
      "Target": "插件目标"
    }
  ],
  "scheduled_tasks": [
    {
      "TaskID": "任务ID",
      "Interval": "执行间隔",
      "StartTime": "开始时间",
      "EndTime": "结束时间",
      "RepeatCount": "重复次数",
      "CurrentCount": "当前执行次数",
      "Status": "任务状态",
      "NextExecution": "下次执行时间",
      "LastExecution": "上次执行时间"
    }
  ]
}
```