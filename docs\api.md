# API 文档

## 任务管理

### 获取所有任务

- **URL**: `/api/v1/tasks`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": 1,
      "name": "任务1",
      "description": "这是任务1的描述",
      "status": "pending"
    }
  ]
  ```

### 创建任务

- **URL**: `/api/v1/tasks`
- **Method**: `POST`
- **Request**:
  ```json
  {
    "name": "新任务",
    "description": "新任务的描述",
    "status": "pending"
  }
  ```
- **Response**:
  ```json
  {
    "id": 2,
    "name": "新任务",
    "description": "新任务的描述",
    "status": "pending"
  }
  ```

## 插件管理

### 获取所有插件

- **URL**: `/api/v1/plugins`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": 1,
      "name": "插件1",
      "version": "1.0",
      "target": "host/email"
    }
  ]
  ```

### 创建插件

- **URL**: `/api/v1/plugins`
- **Method**: `POST`
- **Request**:
  ```json
  {
    "name": "新插件",
    "version": "1.0",
    "target": "host/email"
  }
  ```
- **Response**:
  ```json
  {
    "id": 2,
    "name": "新插件",
    "version": "1.0",
    "target": "host/email"
  }
  ```