<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - Phish X</title>
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Materialize CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --accent-color: #FF9800;
            --warn-color: #F44336;
            --background-color: #f5f5f5;
            --surface-color: #ffffff;
            --text-primary: rgba(0, 0, 0, 0.87);
            --text-secondary: rgba(0, 0, 0, 0.54);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .register-card {
            width: 100%;
            max-width: 400px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .register-header {
            padding: 20px;
            text-align: center;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 8px 8px 0 0;
        }

        .register-body {
            padding: 20px;
        }

        .input-field {
            margin-top: 1rem;
            margin-bottom: 1rem;
        }

        .btn-register {
            width: 100%;
            background-color: var(--secondary-color);
        }

        .register-footer {
            text-align: center;
            padding: 10px;
        }

        .register-footer a {
            color: var(--secondary-color);
            text-decoration: none;
        }

        .register-footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="register-card">
                <div class="register-header">
                    <h4>Phish X 注册</h4>
                    <p>创建一个新账户以开始使用平台</p>
                </div>
                <div class="register-body">
                    <form id="registerForm">
                        <div class="input-field">
                            <i class="material-icons prefix">account_circle</i>
                            <input id="username" type="text" class="validate" required>
                            <label for="username">用户名</label>
                        </div>
                        <div class="input-field">
                            <i class="material-icons prefix">email</i>
                            <input id="email" type="email" class="validate" required>
                            <label for="email">邮箱</label>
                        </div>
                        <div class="input-field">
                            <i class="material-icons prefix">lock</i>
                            <input id="password" type="password" class="validate" required>
                            <label for="password">密码</label>
                        </div>
                        <div class="input-field">
                            <i class="material-icons prefix">lock</i>
                            <input id="confirmPassword" type="password" class="validate" required>
                            <label for="confirmPassword">确认密码</label>
                        </div>
                        <button class="btn btn-register waves-effect waves-light" type="submit" name="action">
                            注册
                            <i class="material-icons right">send</i>
                        </button>
                    </form>
                </div>
                <div class="register-footer">
                    <p>已有账户? <a href="/login">立即登录</a></p>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Materialize components
            $('input').characterCounter();
            
            // Handle form submission
            $('#registerForm').submit(function(e) {
                e.preventDefault();
                
                const username = $('#username').val();
                const email = $('#email').val();
                const password = $('#password').val();
                const confirmPassword = $('#confirmPassword').val();
                
                // Check if passwords match
                if (password !== confirmPassword) {
                    alert('密码和确认密码不匹配');
                    return;
                }
                
                // Send register request
                $.ajax({
                    url: '/api/v1/register',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        username: username,
                        email: email,
                        password: password
                    }),
                    success: function(result) {
                        alert('注册成功! 请登录.');
                        window.location.href = '/login';
                    },
                    error: function(xhr, status, error) {
                        alert('注册失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误'));
                    }
                });
            });
        });
    </script>
</body>
</html>