<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - AI Phish</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-center">注册</h3>
                    </div>
                    <div class="card-body">
                        <form id="registerForm">
                            <div class="form-group">
                                <label for="username">用户名</label>
                                <input type="text" class="form-control" id="username" required>
                            </div>
                            <div class="form-group">
                                <label for="email">邮箱</label>
                                <input type="email" class="form-control" id="email" required>
                            </div>
                            <div class="form-group">
                                <label for="password">密码</label>
                                <input type="password" class="form-control" id="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary btn-block">注册</button>
                        </form>
                        <div class="text-center mt-3">
                            <p>已有账户? <a href="/login">登录</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#registerForm').submit(function(e) {
                e.preventDefault();
                
                const username = $('#username').val();
                const email = $('#email').val();
                const password = $('#password').val();
                
                $.ajax({
                    url: '/auth/register',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ username, email, password }),
                    success: function(result) {
                        alert('注册成功!');
                        window.location.href = '/login';
                    },
                    error: function(xhr, status, error) {
                        alert('注册失败: ' + xhr.responseJSON.error);
                    }
                });
            });
        });
    </script>
</body>
</html>