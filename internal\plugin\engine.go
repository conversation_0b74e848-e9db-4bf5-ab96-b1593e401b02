package plugin

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// PluginEngine manages plugins
type PluginEngine struct {
	PluginPath string
}

// PluginRequest is the request structure for a plugin
type PluginRequest struct {
	Name   string                 `json:"name"`
	Target string                 `json:"target"`
	Email  *EmailData             `json:"email"`
	Params map[string]interface{} `json:"params"`
}

// EmailData represents email data that can be processed by plugins
type EmailData struct {
	From        string            `json:"from"`
	To          []string          `json:"to"`
	Subject     string            `json:"subject"`
	Body        string            `json:"body"`
	Headers     map[string]string `json:"headers"`
	TrackingID  string            `json:"tracking_id"`
}

// PluginResponse is the response structure from a plugin
type PluginResponse struct {
	Status string     `json:"status"`
	Email  *EmailData `json:"email"`
	Detail string     `json:"detail"`
}

// PluginInfo represents information about a plugin
type PluginInfo struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Version     string `json:"version"`
	Type        string `json:"type"` // email_processor, vulnerability_scanner, etc.
}

// NewPluginEngine creates a new plugin engine
func NewPluginEngine(pluginPath string) *PluginEngine {
	return &PluginEngine{
		PluginPath: pluginPath,
	}
}

// RunPlugin runs a plugin
func (p *PluginEngine) RunPlugin(request PluginRequest) (*PluginResponse, error) {
	log.Printf("Running plugin: %s", request.Name)

	// Convert request to JSON
	requestJSON, err := json.Marshal(request)
	if err != nil {
		log.Printf("Failed to marshal plugin request: %v", err)
		return nil, err
	}

	// Run plugin
	pluginPath := filepath.Join(p.PluginPath, request.Name+".py")

	// Check if plugin file exists
	if _, err := os.Stat(pluginPath); os.IsNotExist(err) {
		log.Printf("Plugin file not found: %s", pluginPath)
		return nil, fmt.Errorf("plugin file not found: %s", pluginPath)
	}

	cmd := exec.Command("python", pluginPath)
	cmd.Stdin = strings.NewReader(string(requestJSON))

	// Get output
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Plugin execution failed: %v, output: %s", err, string(output))
		return nil, fmt.Errorf("plugin execution failed: %v, output: %s", err, string(output))
	}

	// Parse response
	var response PluginResponse
	err = json.Unmarshal(output, &response)
	if err != nil {
		log.Printf("Failed to unmarshal plugin response: %v", err)
		return nil, fmt.Errorf("failed to unmarshal plugin response: %v", err)
	}

	log.Printf("Plugin %s executed successfully", request.Name)
	return &response, nil
}

// ListPlugins lists all available plugins
func (p *PluginEngine) ListPlugins() ([]string, error) {
	log.Printf("Listing plugins in directory: %s", p.PluginPath)

	// Check if plugin directory exists
	if _, err := os.Stat(p.PluginPath); os.IsNotExist(err) {
		log.Printf("Plugin directory not found: %s", p.PluginPath)
		return nil, fmt.Errorf("plugin directory not found: %s", p.PluginPath)
	}

	// Read directory
	entries, err := os.ReadDir(p.PluginPath)
	if err != nil {
		log.Printf("Failed to read plugin directory: %v", err)
		return nil, err
	}

	// Filter Python files
	var plugins []string
	for _, entry := range entries {
		if !entry.IsDir() && filepath.Ext(entry.Name()) == ".py" {
			// Remove .py extension
			pluginName := strings.TrimSuffix(entry.Name(), ".py")
			plugins = append(plugins, pluginName)
		}
	}

	log.Printf("Found %d plugins", len(plugins))
	return plugins, nil
}

// GetPluginInfo gets information about a plugin by running it with a special parameter
func (p *PluginEngine) GetPluginInfo(pluginName string) (*PluginInfo, error) {
	log.Printf("Getting info for plugin: %s", pluginName)

	// Create a request to get plugin info
	request := PluginRequest{
		Name:   pluginName,
		Target: "info",
		Params: map[string]interface{}{
			"action": "info",
		},
	}

	// Run plugin
	response, err := p.RunPlugin(request)
	if err != nil {
		log.Printf("Failed to get plugin info: %v", err)
		return nil, err
	}

	// Parse plugin info from response detail
	var info PluginInfo
	err = json.Unmarshal([]byte(response.Detail), &info)
	if err != nil {
		log.Printf("Failed to parse plugin info: %v", err)
		return nil, err
	}

	log.Printf("Got plugin info for %s", pluginName)
	return &info, nil
}