<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件测试 - AI Phish</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <a class="navbar-brand" href="/">AI Phish</a>
        <div class="collapse navbar-collapse">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/tasks">任务管理</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/plugins">插件管理</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/email-test">邮件测试</a>
                </li>
            </ul>
            <ul class="navbar-nav ml-auto" id="authSection">
                <!-- Auth buttons will be inserted here by JavaScript -->
            </ul>
        </div>
    </nav>

    <div class="container mt-4">
        <h2>邮件测试</h2>
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">发送测试邮件</h5>
                <p class="card-text">点击下面的按钮发送一封测试邮件。</p>
                <button id="sendTestEmailBtn" class="btn btn-primary">发送测试邮件</button>
                <div id="result" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (!token) {
                // Redirect to login page if not logged in
                window.location.href = '/login';
                return;
            }
            
            // Show user info and logout button
            $('#authSection').html(`
                <li class="nav-item">
                    <a class="nav-link" href="#" id="logoutBtn">退出登录</a>
                </li>
            `);
            
            $('#logoutBtn').click(function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                window.location.href = '/login';
            });
            
            // Send test email
            $('#sendTestEmailBtn').click(function() {
                $('#result').html('<div class="alert alert-info">正在发送测试邮件...</div>');
                
                $.ajax({
                    url: '/api/v1/send-test-email',
                    method: 'POST',
                    headers: {
                        'Authorization': token
                    },
                    success: function(result) {
                        $('#result').html('<div class="alert alert-success">' + result.message + '</div>');
                    },
                    error: function(xhr, status, error) {
                        if (xhr.status === 401) {
                            alert('登录已过期，请重新登录');
                            localStorage.removeItem('token');
                            window.location.href = '/login';
                        } else {
                            $('#result').html('<div class="alert alert-danger">发送失败: ' + xhr.responseJSON.error + '</div>');
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>