<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件管理 - AI Phish</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <a class="navbar-brand" href="/">AI Phish</a>
        <div class="collapse navbar-collapse">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/tasks">任务管理</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/plugins">插件管理</a>
                </li>
            </ul>
            <ul class="navbar-nav ml-auto" id="authSection">
                <!-- Auth buttons will be inserted here by JavaScript -->
            </ul>
        </div>
    </nav>

    <div class="container mt-4">
        <h2>插件管理</h2>
        <button class="btn btn-primary mb-3" data-toggle="modal" data-target="#createPluginModal">创建插件</button>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>版本</th>
                    <th>目标</th>
                </tr>
            </thead>
            <tbody id="pluginsTableBody">
                <!-- Plugins will be loaded here by JavaScript -->
            </tbody>
        </table>
    </div>

    <!-- Create Plugin Modal -->
    <div class="modal fade" id="createPluginModal" tabindex="-1" aria-labelledby="createPluginModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createPluginModalLabel">创建插件</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="createPluginForm">
                        <div class="form-group">
                            <label for="pluginName">名称</label>
                            <input type="text" class="form-control" id="pluginName" required>
                        </div>
                        <div class="form-group">
                            <label for="pluginVersion">版本</label>
                            <input type="text" class="form-control" id="pluginVersion" required>
                        </div>
                        <div class="form-group">
                            <label for="pluginTarget">目标</label>
                            <input type="text" class="form-control" id="pluginTarget" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitPlugin">创建</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (!token) {
                // Redirect to login page if not logged in
                window.location.href = '/login';
                return;
            }
            
            // Show user info and logout button
            $('#authSection').html(`
                <li class="nav-item">
                    <a class="nav-link" href="#" id="logoutBtn">退出登录</a>
                </li>
            `);
            
            $('#logoutBtn').click(function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                window.location.href = '/login';
            });
            
            // Load plugins
            loadPlugins();
            
            // Submit plugin
            $('#submitPlugin').click(function() {
                const name = $('#pluginName').val();
                const version = $('#pluginVersion').val();
                const target = $('#pluginTarget').val();

                $.ajax({
                    url: '/api/v1/plugins',
                    method: 'POST',
                    contentType: 'application/json',
                    headers: {
                        'Authorization': token
                    },
                    data: JSON.stringify({ name, version, target }),
                    success: function(result) {
                        $('#createPluginModal').modal('hide');
                        loadPlugins();
                    },
                    error: function(xhr, status, error) {
                        alert('创建插件失败: ' + xhr.responseJSON.error);
                    }
                });
            });
        });
        
        function loadPlugins() {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: '/api/v1/plugins',
                method: 'GET',
                headers: {
                    'Authorization': token
                },
                success: function(plugins) {
                    let pluginsHtml = '';
                    plugins.forEach(plugin => {
                        pluginsHtml += `
                            <tr>
                                <td>${plugin.id}</td>
                                <td>${plugin.name}</td>
                                <td>${plugin.version}</td>
                                <td>${plugin.target}</td>
                            </tr>
                        `;
                    });
                    $('#pluginsTableBody').html(pluginsHtml);
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        alert('登录已过期，请重新登录');
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        alert('加载插件失败: ' + xhr.responseJSON.error);
                    }
                }
            });
        }
    </script>
</body>
</html>