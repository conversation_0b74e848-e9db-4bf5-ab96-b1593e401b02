import json
import sys
import uuid
import urllib.parse

def get_info():
    # Return plugin information
    info = {
        "name": "session_hijacking",
        "description": "会话劫持模块插件，通过恶意链接跟踪用户点击行为",
        "version": "1.0.0"
    }
    return info

def generate_tracking_id():
    """
    Generate a unique tracking ID
    """
    return str(uuid.uuid4())

def generate_tracking_url(base_url, tracking_id, additional_params=None):
    """
    Generate a tracking URL with tracking parameters
    """
    # Base tracking parameters
    params = {
        'tid': tracking_id,  # Tracking ID
        'utm_source': 'email',
        'utm_medium': 'phishing'
    }
    
    # Add additional parameters if provided
    if additional_params:
        params.update(additional_params)
    
    # Encode parameters
    query_string = urllib.parse.urlencode(params)
    
    # Construct full URL
    if '?' in base_url:
        return f"{base_url}&{query_string}"
    else:
        return f"{base_url}?{query_string}"

def run(target, email, params):
    # Get action from params
    action = params.get("action")
    
    # If action is 'info', return plugin information
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # Session hijacking logic
    try:
        # Get parameters
        malicious_domain = params.get("malicious_domain", "attacker.com")
        tracking_path = params.get("tracking_path", "/track")
        additional_params = params.get("additional_params", {})
        
        # Generate tracking ID
        tracking_id = generate_tracking_id()
        
        # Generate tracking URL
        tracking_url = generate_tracking_url(
            f"https://{malicious_domain}{tracking_path}",
            tracking_id,
            additional_params
        )
        
        # Create a copy of the email data to avoid modifying the original
        modified_email = email.copy()
        
        # Modify email body to include tracking URL
        original_body = modified_email.get('body', '')
        tracking_link_text = params.get("tracking_link_text", "Click here for more information")
        modified_body = f"{original_body}\n\n{tracking_link_text}: {tracking_url}"
        modified_email["body"] = modified_body
        
        # Add tracking information to email headers
        if "headers" not in modified_email:
            modified_email["headers"] = {}
        modified_email["headers"]["X-Tracking-ID"] = tracking_id
        modified_email["headers"]["X-Target-Email"] = target
        
        # Add a comment to the email body to indicate session hijacking
        modified_email["body"] = f"{modified_email['body']}\n\n<!-- Session Hijacking Plugin Applied -->"
        
        result = {
            "status": "success",
            "email": modified_email,
            "detail": f"Tracking URL added: {tracking_url}"
        }
        return result
    except Exception as e:
        result = {
            "status": "error",
            "detail": f"Failed to apply session hijacking: {str(e)}"
        }
        return result

if __name__ == "__main__":
    # Read input from stdin
    data = json.loads(sys.stdin.read())
    
    # Extract target, email and parameters
    target = data.get("target")
    email = data.get("email", {})
    params = data.get("params", {})
    
    # Run the plugin logic
    res = run(target, email, params)
    
    # Output result to stdout
    print(json.dumps(res))