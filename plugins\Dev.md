# 插件系统使用说明

## 概述

Phish X平台的插件系统允许用户通过Python脚本扩展平台功能，实现各种邮件伪造和安全测试技术。插件通过标准输入/输出与主程序通信，使用JSON格式交换数据。

## 插件架构

### 数据流
1. 主程序调用插件时，通过标准输入传递JSON格式的请求数据
2. 插件处理数据后，通过标准输出返回JSON格式的响应数据
3. 主程序解析响应数据，并根据返回的email数据更新邮件内容

### 数据结构

#### 插件请求 (PluginRequest)
```json
{
  "name": "插件名称",
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {
    "参数名": "参数值"
  }
}
```

#### 插件响应 (PluginResponse)
```json
{
  "status": "success|error",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "detail": "详细信息"
}
```

## 开发插件

### 基本要求
1. 插件必须是Python脚本文件，放在`plugins`目录下
2. 插件必须实现`run(target, email, params)`函数处理邮件数据
3. 插件必须实现`get_info()`函数返回插件信息
4. 插件必须在`__main__`块中处理输入输出

### 示例插件模板
```python
import json
import sys

def get_info():
    # Return plugin information
    info = {
        "name": "example_plugin",
        "description": "插件描述",
        "version": "1.0.0"
    }
    return info

def run(target, email, params):
    # 获取操作类型
    action = params.get("action")
    
    # 如果是info操作，返回插件信息
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # 插件核心逻辑
    # 在这里实现具体的邮件处理逻辑
    # 可以修改email对象的任何字段
    modified_email = email.copy()
    modified_email["body"] = f"{modified_email.get('body', '')}\n\n<!-- 插件处理标记 -->"
    
    result = {
        "status": "success",
        "email": modified_email,
        "detail": "插件执行成功"
    }
    return result

if __name__ == "__main__":
    # 从stdin读取输入
    data = json.loads(sys.stdin.read())
    
    # 提取目标、邮件数据和参数
    target = data.get("target")
    email = data.get("email", {})
    params = data.get("params", {})
    
    # 执行插件逻辑
    res = run(target, email, params)
    
    # 输出结果到stdout
    print(json.dumps(res))
```

## 插件开发注意事项

### 字段限制
由于系统架构限制，插件只能修改`EmailData`结构体中定义的字段：
- `from`: 发件人邮箱地址
- `to`: 收件人邮箱地址列表
- `subject`: 邮件主题
- `body`: 邮件正文
- `headers`: 自定义头部字段（字典类型）
- `tracking_id`: 跟踪ID

任何其他字段都会被忽略。

### 特殊字段处理
1. **附件**: 系统当前不支持附件功能，不要尝试添加`attachments`字段
2. **邮件发送者**: 如果需要修改SMTP的`MAIL FROM`命令参数，应将信息存储在`headers`字段中，例如使用`X-SPF-Bypass-Mail-From`这样的自定义头部

### 错误处理
插件应妥善处理各种错误情况，并返回适当的错误信息：
```python
try:
    # 插件逻辑
    result = {
        "status": "success",
        "email": modified_email,
        "detail": "处理成功"
    }
except Exception as e:
    result = {
        "status": "error",
        "detail": f"处理失败: {str(e)}"
    }
```

## 调试插件

### 本地测试
可以使用以下命令测试插件：
```bash
echo '{
  "target": "<EMAIL>",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "测试邮件",
    "body": "这是一封测试邮件",
    "headers": {},
    "tracking_id": "12345"
  },
  "params": {}
}' | python your_plugin.py
```

### 日志记录
插件可以使用`print()`函数输出调试信息，但请注意这些信息会与返回的JSON数据混合输出。

## 最佳实践

1. **性能考虑**: 插件应尽量高效执行，避免长时间阻塞
2. **安全性**: 插件不应包含恶意代码，应专注于安全测试目的
3. **兼容性**: 确保插件在不同Python版本下都能正常运行
4. **文档**: 为插件提供清晰的文档说明参数和使用方法