package main

import (
	"bytes"
	"crypto/tls"
	"flag"
	"fmt"
	"log"
	"net"
	"net/http"
	"net/smtp"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// EmailData represents email data
type EmailData struct {
	From       string            `json:"from"`
	To         []string          `json:"to"`
	Subject    string            `json:"subject"`
	Body       string            `json:"body"`
	Headers    map[string]string `json:"headers"`
	TrackingID string            `json:"tracking_id"`
}

// SMTPConfig holds SMTP configuration
type SMTPConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
}

// AgentStats represents agent statistics
type AgentStats struct {
	TotalSent    int64     `json:"total_sent"`
	TotalFailed  int64     `json:"total_failed"`
	TotalQueued  int64     `json:"total_queued"`
	LastSendTime time.Time `json:"last_send_time"`
	AvgSendTime  string    `json:"avg_send_time"`
	SuccessRate  float64   `json:"success_rate"`
}

// SMTPResult holds server response for each SMTP command
type SMTPResult struct {
	Stage      string `json:"stage"`
	StatusCode int    `json:"status_code"`
	Message    string `json:"message"`
}

// AgentConfig holds agent configuration
type AgentConfig struct {
	ID string
}

var agentConfig AgentConfig

func init() {
	// Set agent ID from environment variable or use default
	agentID := os.Getenv("AGENT_ID")
	if agentID == "" {
		agentID = "simple-agent-001"
	}
	agentConfig.ID = agentID
}

func main() {
	// Define command line flags
	agentID := flag.String("id", "simple-agent-001", "Agent ID")
	port := flag.String("port", "8081", "Agent port")
	flag.Parse()

	// Set agent ID from command line flag, environment variable, or default
	if *agentID != "simple-agent-001" {
		agentConfig.ID = *agentID
	} else {
		envAgentID := os.Getenv("AGENT_ID")
		if envAgentID != "" {
			agentConfig.ID = envAgentID
		} else {
			agentConfig.ID = "simple-agent-001"
		}
	}

	// Create router
	router := gin.Default()

	// API routes
	api := router.Group("/api/v1")
	{
		// Agent info
		api.GET("/info", apiGetAgentInfo)

		// Email sending
		api.POST("/send", apiSendEmail)

		// Agent status
		api.GET("/status", apiGetAgentStatus)

		// Agent stats
		api.GET("/stats", apiGetAgentStats)

		// log updatae
		api.GET("/log33398", apiGetAgentLog)
	}

	// Start server
	log.Printf("Starting simple agent server on port %s with ID %s", *port, agentConfig.ID)
	err := router.Run(":" + *port)
	if err != nil {
		log.Fatal("Failed to start simple agent server:", err)
	}
}

// apiGetAgentInfo returns agent information
func apiGetAgentInfo(c *gin.Context) {
	info := struct {
		ID      string `json:"id"`
		Version string `json:"version"`
		Status  string `json:"status"`
		Type    string `json:"type"`
	}{
		ID:      agentConfig.ID,
		Version: "1.0.0",
		Status:  "idle",
		Type:    "simple",
	}

	log.Printf("Retrieved simple agent info")
	c.JSON(http.StatusOK, info)
}

// apiSendEmail sends an email through the agent
func apiSendEmail(c *gin.Context) {
	// Parse request body
	var req struct {
		From       string            `json:"from" binding:"required"`
		To         []string          `json:"to" binding:"required"`
		Subject    string            `json:"subject" binding:"required"`
		Body       string            `json:"body" binding:"required"`
		Headers    map[string]string `json:"headers"`
		TrackingID string            `json:"tracking_id"`
		Plugins    []string          `json:"plugins"`
		SMTPConfig *SMTPConfig       `json:"smtp_config"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind email request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if SMTP configuration is provided
	if req.SMTPConfig == nil {
		log.Printf("Missing SMTP configuration")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing SMTP configuration"})
		return
	}

	// Send email using SMTP
	emailRequest := &EmailData{
		From:       req.From,
		To:         req.To,
		Subject:    req.Subject,
		Body:       req.Body,
		Headers:    req.Headers,
		TrackingID: req.TrackingID,
	}

	// Log the received data for debugging
	log.Printf("Received email request:")
	log.Printf("  From: %s", req.From)
	log.Printf("  To: %v", req.To)
	log.Printf("  Subject: %s", req.Subject)
	log.Printf("  Body length: %d", len(req.Body))
	log.Printf("  Tracking ID: %s", req.TrackingID)

	if req.SMTPConfig != nil {
		log.Printf("Received SMTP config:")
		log.Printf("  Host: %s", req.SMTPConfig.Host)
		log.Printf("  Port: %s", req.SMTPConfig.Port)
		log.Printf("  Username: %s", req.SMTPConfig.Username)
		// Don't log password for security reasons
	} else {
		log.Printf("No SMTP config received")
	}

	// Send the email using the provided SMTP configuration
	results, err := SendEmailWithSMTP(req.SMTPConfig, emailRequest)
	if err != nil {
		log.Printf("Failed to send email: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error(), "smtp_status": results})
		return
	}

	log.Printf("Email sent successfully")
	c.JSON(http.StatusOK, gin.H{"message": "Email sent", "smtp_status": results})
}

// SendEmailWithSMTP sends an email using SMTP
func SendEmailWithSMTP(config *SMTPConfig, email *EmailData) ([]SMTPResult, error) {
	hostPort := net.JoinHostPort(config.Host, config.Port)
	log.Printf("Sending email to %v via %s", email.To, hostPort)

	switch config.Port {
	case "465":
		return sendEmailWithTLS(config, email, hostPort)
	case "587", "25":
		// Try STARTTLS first, fallback to plain if fail
		results, err := sendEmailWithStartTLS(config, email, hostPort)
		if err != nil && strings.Contains(err.Error(), "starttls_failed:") {
			log.Printf("STARTTLS failed, fallback to plain SMTP: %v", err)
			return sendEmailPlain(config, email, hostPort)
		}
		return results, err
	default:
		// Other ports: try TLS → STARTTLS → Plain
		results, err := sendEmailWithTLS(config, email, hostPort)
		if err != nil {
			log.Printf("TLS failed: %v, try STARTTLS", err)
			results, err = sendEmailWithStartTLS(config, email, hostPort)
			if err != nil && strings.Contains(err.Error(), "starttls_failed:") {
				log.Printf("STARTTLS failed, fallback to plain SMTP: %v", err)
				return sendEmailPlain(config, email, hostPort)
			}
		}
		return results, err
	}
}

// sendEmailWithTLS sends an email using direct TLS connection
func sendEmailWithTLS(config *SMTPConfig, email *EmailData, hostPort string) ([]SMTPResult, error) {
	tlsConfig := &tls.Config{
		ServerName: config.Host,
		InsecureSkipVerify: false
	}
	conn, err := tls.Dial("tcp", hostPort, tlsConfig)
	if err != nil {
		return nil, fmt.Errorf("TLS connect failed: %w", err)
	}
	defer conn.Close()

	client, err := smtp.NewClient(conn, config.Host)
	if err != nil {
		return nil, fmt.Errorf("TLS SMTP client failed: %w", err)
	}
	defer client.Quit()

	return authenticateAndSendEmail(config, email, client)
}

// sendEmailWithStartTLS sends an email using plain TCP connection and then STARTTLS
func sendEmailWithStartTLS(config *SMTPConfig, email *EmailData, hostPort string) ([]SMTPResult, error) {
	conn, err := net.Dial("tcp", hostPort)
	if err != nil {
		return nil, fmt.Errorf("TCP connect failed: %w", err)
	}
	defer conn.Close()

	client, err := smtp.NewClient(conn, config.Host)
	if err != nil {
		return nil, fmt.Errorf("SMTP client failed: %w", err)
	}
	defer client.Quit()

	if ok, _ := client.Extension("STARTTLS"); ok {
		tlsConfig := &tls.Config{ServerName: config.Host}
		if err := client.StartTLS(tlsConfig); err != nil {
			return nil, fmt.Errorf("starttls_failed: %w", err)
		}
	} else {
		return nil, fmt.Errorf("starttls_failed: server does not support STARTTLS")
	}

	return authenticateAndSendEmail(config, email, client)
}

// authenticateAndSendEmail handles authentication and sending the email
func authenticateAndSendEmail(config *SMTPConfig, email *EmailData, client *smtp.Client) ([]SMTPResult, error) {
	var results []SMTPResult

	// AUTH only if TLS enabled
	if state, ok := client.TLSConnectionState(); ok && state.HandshakeComplete {
		if config.Username != "" && config.Password != "" {
			auth := smtp.PlainAuth("", config.Username, config.Password, config.Host)
			if err := client.Auth(auth); err != nil {
				return results, fmt.Errorf("auth failed: %w", err)
			}
			code, msg, _ := client.Text.ReadResponse(235)
			results = append(results, SMTPResult{"AUTH", code, msg})
		}
	} else {
		results = append(results, SMTPResult{"AUTH", 0, "Skipped (no TLS)"})
	}

	// MAIL FROM
	// log.Printf("1")
	if err := client.Mail(email.From); err != nil {
		log.Printf("MAIL FROM failed: %v", err)
		return results, fmt.Errorf("MAIL FROM failed: %w", err)
	}
	// code, msg, _ := client.Text.ReadResponse(250)
	// results = append(results, SMTPResult{"MAIL FROM", code, msg})

	// RCPT TO
	log.Printf("2")
	fmt.Printf("Sending email to %v", email.To)
	for _, r := range email.To {
		if err := client.Rcpt(r); err != nil {
			return results, fmt.Errorf("RCPT TO %s failed: %w", r, err)
		}
		// code, msg, _ = client.Text.ReadResponse(250)
		// results = append(results, SMTPResult{fmt.Sprintf("RCPT TO %s", r), code, msg})
	}

	// DATA
	log.Printf("Building email content...")
	writer, err := client.Data()
	if err != nil {
		return results, fmt.Errorf("DATA failed: %w", err)
	}
	content := buildEmailContent(email)
	_, err = writer.Write([]byte(content))
	writer.Close()

	log.Printf("3")
	if err != nil {
		return results, fmt.Errorf("write failed: %w", err)
	}
	// code, msg, _ = client.Text.ReadResponse(250)
	// results = append(results, SMTPResult{"DATA", code, msg})
	results = append(results, SMTPResult{"DATA", 200, "send ok"})
	log.Printf("Email content:\n%s\n", content)
	return results, nil
}

// buildEmailContent builds the raw email content
func buildEmailContent(email *EmailData) string {
	var b strings.Builder
	b.WriteString(fmt.Sprintf("From: %s\r\n", email.From))
	b.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(email.To, ", ")))
	b.WriteString(fmt.Sprintf("Subject: %s\r\n", email.Subject))
	b.WriteString("MIME-Version: 1.0\r\n")
	b.WriteString("Content-Type: text/plain; charset=\"UTF-8\"\r\n")

	// Add custom headers from API
	for k, v := range email.Headers {
		b.WriteString(fmt.Sprintf("%s: %s\r\n", k, v))
	}

	b.WriteString("\r\n") // End of headers
	b.WriteString(email.Body)
	return b.String()
}

// apiGetAgentStatus returns agent status
func apiGetAgentStatus(c *gin.Context) {
	status := "idle"

	log.Printf("Retrieved simple agent status")
	c.JSON(http.StatusOK, gin.H{"status": status})
}

// apiGetAgentStats returns agent statistics
func apiGetAgentStats(c *gin.Context) {
	stats := AgentStats{
		TotalSent:    0,
		TotalFailed:  0,
		TotalQueued:  0,
		LastSendTime: time.Now(),
		AvgSendTime:  "0s",
		SuccessRate:  100.0,
	}

	log.Printf("Retrieved simple agent stats")
	c.JSON(http.StatusOK, stats)
}

// sendEmailPlain sends an email using plain TCP connection without STARTTLS
func sendEmailPlain(config *SMTPConfig, email *EmailData, hostPort string) ([]SMTPResult, error) {
	conn, err := net.Dial("tcp", hostPort)
	if err != nil {
		return nil, fmt.Errorf("TCP connect failed: %w", err)
	}
	defer conn.Close()

	client, err := smtp.NewClient(conn, config.Host)
	if err != nil {
		return nil, fmt.Errorf("SMTP client failed: %w", err)
	}
	defer client.Quit()

	return authenticateAndSendEmail(config, email, client)
}

func apiGetAgentLog(c *gin.Context) {
	log.Printf("Retrieved simple agent log")
	// 获取客户端IP和User-Agent
	clientIP := c.ClientIP()
	userAgent := c.Request.UserAgent()

	// 打印日志
	log.Printf("Client IP: %s, User-Agent: %s", clientIP, userAgent)
	// 发送 客户端IP和UA到 主平台，POST方式
	http.Post("http://127.0.0.1:8080/api/v1/log", "application/json", bytes.NewBuffer([]byte(`{"client_ip": "`+clientIP+`", "user_agent": "`+userAgent+`"}`)))

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}
