@echo off

REM Phish X Deployment Script for Windows

REM Set the project name
set PROJECT_NAME=aiphish

REM Set the output directory
set OUTPUT_DIR=dist

REM Set the main binary name
set MAIN_BINARY_NAME=aiphish.exe

REM Set the agent binary name
set AGENT_BINARY_NAME=agent.exe

REM Create the output directory if it doesn't exist
if not exist %OUTPUT_DIR% mkdir %OUTPUT_DIR%

REM Build the main application
echo Building the main application...
go build -o %OUTPUT_DIR%/%MAIN_BINARY_NAME% .

REM Check if the main build was successful
if %errorlevel% neq 0 (
    echo Main application build failed!
    exit /b 1
)

REM Build the agent
echo Building the agent...
cd agent
go build -o ..\%OUTPUT_DIR%\%AGENT_BINARY_NAME% .
cd ..

REM Check if the agent build was successful
if %errorlevel% neq 0 (
    echo Agent build failed!
    exit /b 1
)

REM Copy necessary files and directories
echo Copying necessary files and directories...

REM Copy the web directory
xcopy /E /I web %OUTPUT_DIR%\web

REM Copy the plugins directory
xcopy /E /I plugins %OUTPUT_DIR%\plugins

REM Copy the docs directory
xcopy /E /I docs %OUTPUT_DIR%\docs

REM Copy the README.md file
copy README.md %OUTPUT_DIR%\README.md

REM Print the deployment summary
echo Deployment completed successfully!
echo The main application and agent have been packaged in the %OUTPUT_DIR% directory.
echo To start the main application, run %MAIN_BINARY_NAME% from the %OUTPUT_DIR% directory.
echo To start the agent, run %AGENT_BINARY_NAME% from the %OUTPUT_DIR% directory.