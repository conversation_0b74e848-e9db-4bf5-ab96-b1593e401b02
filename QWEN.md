# 📌 产品与技术方案文档

## 一、产品定位

一个 **轻便型、插件化、可扩展** 的社会工程安全测试平台，核心围绕 **邮件发送 (Agent 模式)** 与 **漏洞插件机制**，支持客户端、服务端、协议层漏洞验证。

---

## 二、产品需求

### 1. 功能需求

* **任务管理**：创建/调度测试任务。
* **邮件发送 Agent**：模拟钓鱼邮件、协议交互（SMTP/POP3/IMAP）。
* **漏洞插件**：加载不同插件执行漏洞验证（XSS/SQLi/POP3溢出等）。
* **结果收集**：统一收集插件执行与 Agent 响应结果。
* **报告输出**：生成 HTML/PDF 报告。

### 2. 能力需求（技术手段）

* **协议构造能力**：SMTP/POP3/IMAP 报文生成与解析。
* **任务调度能力**：统一调度邮件发送与插件任务。
* **插件机制**：动态加载 Python/Golang 插件。
* **大模型能力**：生成钓鱼邮件文案、总结漏洞扫描结果。
* **轻存储能力**：SQLite 存储任务、结果与配置。

---

## 三、技术架构

### 1. 总体架构

```
[Web UI / CLI / 外部系统] --API--> [核心控制器 (aiphish)]
                                          |
                                          |-- [任务调度器]
                                          |-- [邮件发送协调器]
                                          |-- [漏洞插件引擎]
                                          |-- [结果存储 (SQLite)]
                                          |-- [报告生成器]
                                          |-- [LLM 辅助模块 (未来)]

[核心控制器] --API--> [邮件发送代理 (agent)]
```

### 2. 模块说明

* **核心控制器 (aiphish)**：主进程，负责任务接收、插件管理、调度和与外部系统的API交互。它不直接发送邮件，而是协调邮件发送代理。
* **邮件发送代理 (agent)**：独立部署的进程，负责实际的邮件发送工作。可以部署在不同的网络环境中。核心控制器通过API调用这些代理来发送邮件。
* **任务调度器**：支持计划任务、并发控制，管理钓鱼任务的执行。
* **邮件发送协调器**：负责与已注册的邮件发送代理进行通信。
* **漏洞插件引擎**：

  * 采用 Python 动态加载。
  * 插件定义标准输入/输出（JSON）。
  * 插件可以修改邮件内容（主题、正文、头部等）。
* **存储**：SQLite，简单、轻便、可迁移。

---

## 四、插件机制设计

* 插件接口定义：

```json
{
  "name": "plugin_name",
  "version": "1.0",
  "target": "host/email",
  "params": {"key": "value"},
  "result": {"status": "success|fail", "detail": "..."}
}
```

* 插件调用方式：

  * Go：通过 `plugin.Open` 加载 `.so` 文件。
  * Python：通过子进程运行 `.py`，JSON 输入输出。
* 插件类别：

  * 邮件协议类（SMTP fuzz、IMAP 命令注入）。
  * Web 漏洞类（XSS、SQLi 测试）。
  * 客户端漏洞类（附件投递、解析异常）。

---

## 五、技术栈

* **后端核心**：Golang（控制器、Agent、调度、存储）。
* **插件扩展**：Python（漏洞 PoC 编写友好）。
* **前端交互**：Gin 模板直出（不做前后端分离）。
* **存储**：SQLite（轻量级 DB）。

---

## 六、实现阶段（MVP -> 增强）

1. **MVP 版本**

   * 邮件发送代理 (Agent) 支持 SMTP 发送。
   * 核心控制器提供任务管理、插件管理和API。
   * 任务调度器 + SQLite 存储。
   * 插件引擎（运行 Python 插件）。
   * 报告生成（HTML）。

2. **增强版本**

   * 支持 POP3/IMAP 协议交互（在Agent中）。
   * 支持插件市场（共享插件）。


3. **进阶版本**

   * 画像系统（结合 OSINT、历史结果）。
   * 多租户 & API Token。
   * 更强的漏洞插件库。

---

## 七、示例代码片段

### 1. Go 邮件 Agent（简化版）

```go
package main

import (
    "crypto/tls"
    "fmt"
    "net/smtp"
)

func sendMail(host, port, from, to, body string) error {
    addr := fmt.Sprintf("%s:%s", host, port)
    auth := smtp.PlainAuth("", from, "password", host)
    conn, err := tls.Dial("tcp", addr, &tls.Config{InsecureSkipVerify: true})
    if err != nil {
        return err
    }
    c, err := smtp.NewClient(conn, host)
    if err != nil {
        return err
    }
    defer c.Quit()
    c.Auth(auth)
    c.Mail(from)
    c.Rcpt(to)
    w, _ := c.Data()
    fmt.Fprintf(w, body)
    w.Close()
    return nil
}
```

### 2. Python 插件示例

```python
# plugin_sql_injection.py
import json, sys

def run(target, params):
    payload = "' OR '1'='1"
    # 模拟请求
    result = {"status": "fail", "detail": f"Target {target} vulnerable with payload {payload}"}
    return result

if __name__ == "__main__":
    data = json.loads(sys.stdin.read())
    target = data.get("target")
    params = data.get("params", {})
    res = run(target, params)
    print(json.dumps(res))
```

---

## 八、总结

该方案强调：

* **轻便**（SQLite、本地部署、单进程多模块）。
* **可扩展**（插件机制支持漏洞扩展）。
* **实用**（邮件 Agent + 漏洞验证闭环）。
* **智能**（结合 LLM 自动化生成与总结）。

## Qwen Added Memories
- 项目要求使用Python或Golang开发，优先Python进行数据分析。需要实现用户登录、数据展示、服务监控和数据分析功能。要求代码简洁、注释详细、逻辑清晰，并有现代化界面。数据库使用SQLite，并采用模型设计。API需接口化设计并提供文档。
