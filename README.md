# AI Phish - 社会工程安全测试平台

## 项目概述

AI Phish 是一个轻便型、插件化、可扩展的社会工程安全测试平台，核心围绕邮件发送 (Agent 模式) 与漏洞插件机制，支持客户端、服务端、协议层漏洞验证。

## 功能需求

* **任务管理**：创建/调度测试任务。
* **邮件发送 Agent**：模拟钓鱼邮件、协议交互（SMTP/POP3/IMAP）。
* **漏洞插件**：加载不同插件执行漏洞验证（XSS/SQLi/POP3溢出等）。
* **结果收集**：统一收集插件执行与 Agent 响应结果。
* **报告输出**：生成 HTML/PDF 报告。

## 技术栈

* **后端核心**：Golang（控制器、Agent、调度、存储）
* **插件扩展**：Python（漏洞 PoC 编写友好）
* **前端交互**：Gin 模板直出（不做前后端分离）
* **存储**：SQLite（轻量级 DB）
* **报告**：Go + wkhtmltopdf 或 ReportLab（PDF）
* **大模型集成**：调用 API（OpenAI/本地 LLM）

## 目录结构

```
aiphish/
├── README.md
├── main.go              # 核心控制器入口
├── config/              # 配置文件目录
│   └── config.yaml      # 配置文件
├── internal/            # 内部模块
│   ├── scheduler/       # 任务调度器
│   ├── agent/           # 邮件发送 Agent
│   ├── plugin/          # 插件引擎
│   ├── storage/         # 存储模块
│   ├── report/          # 报告生成器
│   └── llm/             # LLM 辅助模块
├── plugins/             # 插件目录
│   └── example_plugin.py # 示例插件
├── web/                 # Web UI 静态文件
│   ├── templates/       # HTML 模板
│   └── static/          # 静态资源
└── docs/                # 文档
    └── api.md           # API 文档
```

## 快速开始

### 安装依赖

```bash
go mod tidy
```

### 运行项目

```bash
go run main.go
```

## 插件开发

插件需要遵循标准输入/输出（JSON）格式。

### 示例插件

```python
# plugins/example_plugin.py
import json
import sys

def run(target, params):
    # 插件逻辑
    result = {
        "status": "success",
        "detail": f"Plugin executed on {target} with params {params}"
    }
    return result

if __name__ == "__main__":
    data = json.loads(sys.stdin.read())
    target = data.get("target")
    params = data.get("params", {})
    res = run(target, params)
    print(json.dumps(res))
```

## API 文档

详见 [docs/api.md](docs/api.md)