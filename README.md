# Phish X - 社会工程安全测试平台

## 项目概述

Phish X 是一个轻便型、插件化、可扩展的社会工程安全测试平台。它由三个核心组件构成：
1. **核心控制器 (aiphish)**: 负责任务管理、插件协调、API提供和与外部系统的交互。
2. **邮件发送代理 (agent)**: 独立部署的进程，负责实际的邮件发送工作。
3. **插件系统 (plugins)**: 实现各种邮件伪造和漏洞利用技术。

平台通过插件机制来丰富邮件内容，实现漏洞测试。

## 功能特性

* **任务管理**：创建/调度测试任务。
* **邮件发送代理管理**：注册、管理多个独立部署的邮件发送代理。
* **漏洞插件**：加载不同插件执行漏洞验证（XSS/SQLi/POP3溢出等），插件可以修改邮件内容。
* **结果收集**：统一收集插件执行与代理响应结果。
* **报告输出**：生成 HTML/PDF 报告。
* **多插件支持**：支持按顺序调用多个插件处理邮件数据。
* **邮件伪造技术**：支持SPF绕过、DKIM签名伪造、视觉欺骗、SMTP走私等多种技术。

## 技术栈

* **后端核心**：Golang（控制器、代理、调度、存储）
* **插件扩展**：Python（漏洞 PoC 编写友好）
* **前端交互**：Gin 模板直出（不做前后端分离）
* **存储**：SQLite（轻量级 DB）
* **报告**：Go + wkhtmltopdf 或 ReportLab（PDF）

## 目录结构

```
aiphish/
├── README.md
├── main.go              # 核心控制器入口
├── internal/            # 内部模块
│   ├── scheduler/       # 任务调度器
│   │   └── phishing_scheduler.go # 钓鱼任务调度
│   ├── agent/           # 邮件发送 Agent
│   │   ├── agent.go     # 邮件发送核心
│   │   ├── manager.go   # Agent 管理
│   │   └── client.go    # Agent 客户端
│   ├── plugin/          # 插件引擎
│   │   └── engine.go    # 插件执行引擎
│   ├── report/          # 报告生成器
│   └── logger/          # 日志记录器
├── plugins/             # 插件目录
│   ├── spf_bypass.py    # SPF绕过插件
│   ├── dkim_forgery.py  # DKIM签名伪造插件
│   ├── visual_deception.py # 视觉欺骗插件
│   ├── smtp_smuggling.py # SMTP走私插件
│   ├── emoji_domain_hijacking.py # 表情符号域名劫持插件
│   ├── svg_code_execution.py # SVG代码执行插件
│   ├── session_hijacking.py # 会话劫持模块插件
│   └── README.md        # 插件开发指南
├── web/                 # Web UI 静态文件
│   ├── templates/       # HTML 模板
│   └── static/          # 静态资源
├── agent/               # 独立的邮件发送代理
│   └── main.go          # Agent入口
└── docs/                # 文档
    └── api.md           # API 文档
```

## 快速开始

### 安装依赖

```bash
go mod tidy
```

### 运行项目

```bash
# 构建项目
./deploy.sh # Linux/macOS
# 或
deploy.bat # Windows

# 进入dist目录
cd dist

# 启动核心控制器 (在第一个终端)
./aiphish # Linux/macOS
# 或
aiphish.exe # Windows

# 启动邮件发送代理 (在第二个终端)
# 方式1: 使用命令行参数指定Agent ID
./agent -id my-agent-001 # Linux/macOS
# 或
agent.exe -id my-agent-001 # Windows

# 方式2: 使用环境变量指定Agent ID
AGENT_ID=my-agent-001 ./agent # Linux/macOS
# 或在Windows上先设置环境变量
set AGENT_ID=my-agent-001
agent.exe

# 方式3: 使用位置参数指定Agent ID
./agent my-agent-001 # Linux/macOS
# 或
agent.exe my-agent-001 # Windows
```

## 插件开发

插件需要遵循标准输入/输出（JSON）格式。

### 示例插件

```python
# plugins/example_plugin.py
import json
import sys

def get_info():
    # Return plugin information
    info = {
        "name": "example_plugin",
        "description": "An example plugin for Phish X",
        "version": "1.0.0"
    }
    return info

def run(target, email, params):
    # Example plugin logic
    # In a real plugin, this would perform some vulnerability check
    action = params.get("action")
    
    # If action is 'info', return plugin information
    if action == "info":
        info = get_info()
        result = {
            "status": "success",
            "detail": json.dumps(info)
        }
        return result
    
    # Otherwise, execute normal plugin logic
    result = {
        "status": "success",
        "detail": f"Executed plugin on {target} with params {params}"
    }
    return result

if __name__ == "__main__":
    # Read input from stdin
    data = json.loads(sys.stdin.read())
    
    # Extract target, email and parameters
    target = data.get("target")
    email = data.get("email", {})
    params = data.get("params", {})
    
    # Run the plugin logic
    res = run(target, email, params)
    
    # Output result to stdout
    print(json.dumps(res))
```

## 邮件伪造技术

平台支持多种邮件伪造技术，通过插件实现：

### SPF绕过
通过分离`MAIL FROM`命令和邮件头部的`From`字段实现。

### DKIM签名伪造
生成伪造的DKIM签名并添加到邮件头部。

### 视觉欺骗
使用Unicode RTL覆盖字符创建视觉欺骗的发件人地址。

### SMTP走私
利用SMTP协议漏洞绕过SPF/DMARC检查。

### 表情符号域名劫持
通过注册包含表情符号的相似域名实现欺骗。

### SVG代码执行
在邮件中嵌入恶意SVG代码实现代码执行。

### 会话劫持
通过恶意链接跟踪用户点击行为。

## Agent管理

### 多Agent支持
平台支持多个邮件发送代理（Agent）的注册和管理，可以实现分布式邮件发送和负载均衡。

### 健康检查和智能选择
平台实现了基于健康检查的智能Agent选择机制：

1. **健康检查**：在选择Agent之前，系统会对所有已注册的Agent进行健康检查
   - 检查Agent的状态（连接、断开、停止、错误等）
   - 验证Agent的连通性
   - 确认Agent的功能是否正常

2. **随机选择**：从健康的Agent中随机选择一个来执行任务
   - 如果只有一个健康的Agent，则选择该Agent
   - 如果有多个健康的Agent，则随机选择其中一个
   - 如果没有健康的Agent，则返回错误

3. **故障隔离**：自动避开不健康的Agent，提高任务执行的成功率

### Agent生命周期管理
- **注册**：Agent可以动态注册到核心控制器
- **监控**：持续监控Agent的状态和性能
- **注销**：Agent可以主动注销或被控制器移除

### Agent ID配置
Agent ID可以通过以下方式配置：
1. **命令行参数**：使用 `-id` 参数指定，如 `agent -id my-agent-001`
2. **环境变量**：设置 `AGENT_ID` 环境变量
3. **位置参数**：作为第一个位置参数传递，如 `agent my-agent-001`
4. **默认值**：如果未指定，则使用默认ID "simple-agent-001"

## 架构说明

### 核心控制器
负责：
- 用户接口（Web UI和API）
- 任务管理
- 插件协调
- Agent管理
- 数据存储
- 报告生成

### 邮件发送代理
负责：
- 实际发送邮件
- 与SMTP服务器通信
- 支持TLS加密和身份验证

### 插件系统
负责：
- 实现各种邮件伪造技术
- 处理邮件数据
- 返回修改后的邮件数据

### 工作流程
1. 用户通过Web UI或API提交邮件任务
2. 核心控制器接收任务并调用相应插件
3. 插件按顺序处理邮件数据，添加伪造信息
4. 核心控制器将处理后的邮件数据发送给通过健康检查随机选择的Agent
5. Agent发送邮件

## API 文档

详见 [docs/api.md](docs/api.md)

## Web界面

平台提供完整的Web管理界面：
- 任务管理：创建、查看、执行任务
- 插件管理：查看插件信息、测试插件
- Agent管理：注册、监控Agent状态
- 数据展示：查看任务执行结果和统计数据
- 任务调度：设置任务执行计划