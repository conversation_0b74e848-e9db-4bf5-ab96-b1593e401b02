<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务调度 - Phish X</title>
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Materialize CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --accent-color: #FF9800;
            --warn-color: #F44336;
            --background-color: #f5f5f5;
            --surface-color: #ffffff;
            --text-primary: rgba(0, 0, 0, 0.87);
            --text-secondary: rgba(0, 0, 0, 0.54);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .sidenav {
            background-color: var(--surface-color);
        }

        .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            margin-bottom: 1rem;
        }

        .card-content {
            padding: 16px;
        }

        .btn-primary {
            background-color: var(--primary-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-accent {
            background-color: var(--accent-color);
        }

        .btn-warn {
            background-color: var(--warn-color);
        }

        .page-header {
            padding: 1rem 0;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(0,0,0,.1);
        }

        .page-title {
            font-weight: 500;
            margin: 0;
        }

        .breadcrumb {
            font-size: 1rem;
        }

        .breadcrumb:before {
            color: var(--text-secondary);
        }

        .breadcrumb:last-child {
            color: var(--text-primary);
        }

        .table-responsive {
            overflow-x: auto;
        }

        .data-table th {
            font-weight: 500;
            color: var(--text-primary);
        }

        .data-table td {
            color: var(--text-secondary);
        }

        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-primary {
            color: #fff;
            background-color: var(--primary-color);
        }

        .badge-secondary {
            color: #fff;
            background-color: var(--secondary-color);
        }

        .badge-accent {
            color: #fff;
            background-color: var(--accent-color);
        }

        .badge-warn {
            color: #fff;
            background-color: var(--warn-color);
        }

        .badge-success {
            color: #fff;
            background-color: #4CAF50;
        }

        .badge-danger {
            color: #fff;
            background-color: #F44336;
        }

        .badge-warning {
            color: #fff;
            background-color: #FF9800;
        }

        .loader {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .toast {
            border-radius: 4px;
        }

        .footer {
            padding: 1rem 0;
            background-color: var(--surface-color);
            border-top: 1px solid rgba(0,0,0,.1);
        }

        .footer-links a {
            color: var(--text-secondary);
            margin-right: 1rem;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-wrapper">
            <a href="/" class="brand-logo">
                <i class="material-icons left" style="margin-left: 10px;">security</i>
                Phish X
            </a>
            <a href="#" data-target="mobile-nav" class="sidenav-trigger">
                <i class="material-icons">menu</i>
            </a>
            <ul class="right hide-on-med-and-down">
                <li><a href="/tasks">任务管理</a></li>
                <li><a href="/plugin-management">插件管理</a></li>
                <li><a href="/email-test">邮件测试</a></li>
                <li class="active"><a href="/scheduler">任务调度</a></li>
                <li><a href="/dashboard">数据展示</a></li>
                <li><a href="/agent-management">Agent管理</a></li>
                <li id="authSection">
                    <!-- Auth buttons will be inserted here by JavaScript -->
                </li>
            </ul>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <ul class="sidenav" id="mobile-nav">
        <li><a href="/tasks">任务管理</a></li>
        <li><a href="/plugin-management">插件管理</a></li>
        <li><a href="/email-test">邮件测试</a></li>
        <li class="active"><a href="/scheduler">任务调度</a></li>
        <li><a href="/dashboard">数据展示</a></li>
        <li><a href="/agent-management">Agent管理</a></li>
        <li id="mobileAuthSection">
            <!-- Auth buttons will be inserted here by JavaScript -->
        </li>
    </ul>

    <!-- Breadcrumbs -->
    <nav class="breadcrumb-container">
        <div class="nav-wrapper">
            <div class="col s12">
                <a href="/" class="breadcrumb">首页</a>
                <a href="/scheduler" class="breadcrumb">任务调度</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="page-header">
                        <h2 class="page-title">任务调度</h2>
                        <p class="lead">调度和管理定时任务，控制任务执行时间。</p>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">调度任务</h5>
                            <button class="btn btn-sm btn-outline-primary" id="refreshScheduledTasks">
                                <i class="material-icons left">refresh</i> 刷新
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>任务ID</th>
                                            <th>状态</th>
                                            <th>执行间隔</th>
                                            <th>下次执行</th>
                                            <th>上次执行</th>
                                            <th>执行次数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="scheduledTasksTableBody">
                                        <!-- Scheduled tasks will be loaded here by JavaScript -->
                                        <tr>
                                            <td colspan="7" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">调度现有任务</h5>
                        </div>
                        <div class="card-body">
                            <form id="scheduleTaskForm">
                                <div class="form-group">
                                    <label for="taskId">选择任务</label>
                                    <select id="taskId" class="browser-default" required>
                                        <option value="" disabled selected>选择一个任务</option>
                                        <!-- Tasks will be loaded here by JavaScript -->
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="interval">执行间隔 (秒)</label>
                                    <input type="number" class="form-control" id="interval" min="1" value="60" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="repeatCount">重复次数 (-1 表示无限)</label>
                                    <input type="number" class="form-control" id="repeatCount" min="-1" value="1" required>
                                </div>
                                
                                <button type="submit" class="btn btn-success">
                                    <i class="material-icons left">schedule</i> 调度任务
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="page-footer">
        <div class="container">
            <div class="row">
                <div class="col l6 s12">
                    <h5 class="white-text">Phish X</h5>
                    <p class="grey-text text-lighten-4">一个轻便型、插件化、可扩展的社会工程安全测试平台。</p>
                </div>
                <div class="col l4 offset-l2 s12">
                    <h5 class="white-text">链接</h5>
                    <ul class="footer-links">
                        <li><a class="grey-text text-lighten-3" href="/">首页</a></li>
                        <li><a class="grey-text text-lighten-3" href="/docs">文档</a></li>
                        <li><a class="grey-text text-lighten-3" href="/support">支持</a></li>
                        <li><a class="grey-text text-lighten-3" href="/about">关于</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-copyright">
            <div class="container">
                © 2023 Phish X. 保留所有权利。
                <a class="grey-text text-lighten-4 right" href="#!">隐私政策</a>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Materialize components
            $('.sidenav').sidenav();
            $('.modal').modal();
            $('.collapsible').collapsible();
            $('.tabs').tabs();
            $('.tooltipped').tooltip();
            $('select').formSelect();
            
            // Check if user is logged in
            const token = localStorage.getItem('token');
            
            if (!token) {
                // Redirect to login page if not logged in
                window.location.href = '/login';
                return;
            }
            
            // Show user info and logout button
            $('#authSection').html(`
                <li class="nav-item">
                    <a class="nav-link waves-effect waves-light btn red lighten-1" href="#" id="logoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#mobileAuthSection').html(`
                <li>
                    <a href="#" id="mobileLogoutBtn">
                        <i class="material-icons left">exit_to_app</i>退出登录
                    </a>
                </li>
            `);
            
            $('#logoutBtn, #mobileLogoutBtn').click(function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                window.location.href = '/login';
            });
            
            // Load scheduled tasks and available tasks
            loadScheduledTasks();
            loadTasksForScheduling();
            
            // Refresh scheduled tasks when button is clicked
            $('#refreshScheduledTasks').click(function() {
                loadScheduledTasks();
            });
            
            // Handle form submission
            $('#scheduleTaskForm').submit(function(e) {
                e.preventDefault();
                scheduleTask();
            });
        });
        
        function loadScheduledTasks() {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: '/api/v1/scheduler/tasks',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(scheduledTasks) {
                    // Clear table
                    $('#scheduledTasksTableBody').empty();
                    
                    // Check if there are scheduled tasks
                    if (!scheduledTasks || scheduledTasks.length === 0) {
                        $('#scheduledTasksTableBody').html('<tr><td colspan="7" class="text-center">暂无调度任务</td></tr>');
                        return;
                    }
                    
                    // Add scheduled tasks to table
                    scheduledTasks.forEach(task => {
                        // Format interval
                        let intervalStr = '';
                        if (task.Interval < 60) {
                            intervalStr = `${task.Interval}秒`;
                        } else if (task.Interval < 3600) {
                            intervalStr = `${Math.floor(task.Interval / 60)}分钟`;
                        } else if (task.Interval < 86400) {
                            intervalStr = `${Math.floor(task.Interval / 3600)}小时`;
                        } else {
                            intervalStr = `${Math.floor(task.Interval / 86400)}天`;
                        }
                        
                        const statusBadge = `<span class="badge badge-${task.Status}">${task.Status}</span>`;
                        
                        $('#scheduledTasksTableBody').append(`
                            <tr>
                                <td>${task.TaskID}</td>
                                <td>${statusBadge}</td>
                                <td>${intervalStr}</td>
                                <td>${task.NextExecution ? new Date(task.NextExecution).toLocaleString('zh-CN') : 'N/A'}</td>
                                <td>${task.LastExecution ? new Date(task.LastExecution).toLocaleString('zh-CN') : 'N/A'}</td>
                                <td>${task.CurrentCount}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary waves-effect waves-light" onclick="runScheduledTask('${task.TaskID}')">
                                        <i class="material-icons">play_arrow</i>
                                    </button>
                                    <button class="btn btn-sm btn-danger waves-effect waves-light" onclick="unscheduleTask('${task.TaskID}')">
                                        <i class="material-icons">delete</i>
                                    </button>
                                </td>
                            </tr>
                        `);
                    });
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        $('#scheduledTasksTableBody').html('<tr><td colspan="7" class="text-center">加载调度任务列表失败</td></tr>');
                        M.toast({html: '加载调度任务列表失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                        console.log('加载调度任务列表失败: ' + xhr.responseJSON.error);
                    }
                }
            });
        }
        
        function loadTasksForScheduling() {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: '/api/v1/tasks',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(tasks) {
                    // Clear select options
                    $('#taskId').empty();
                    $('#taskId').append('<option value="" disabled selected>选择一个任务</option>');
                    
                    // Check if there are tasks
                    if (!tasks || tasks.length === 0) {
                        $('#taskId').append('<option value="" disabled>暂无任务</option>');
                        $('#taskId').formSelect(); // Re-initialize select
                        return;
                    }
                    
                    // Add tasks to select
                    tasks.forEach(task => {
                        $('#taskId').append(`<option value="${task.ID}">${task.Name}</option>`);
                    });
                    
                    // Re-initialize select
                    $('#taskId').formSelect();
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        $('#taskId').empty();
                        $('#taskId').append('<option value="" disabled>加载任务列表失败</option>');
                        $('#taskId').formSelect(); // Re-initialize select
                        M.toast({html: '加载任务列表失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                        console.log('加载任务列表失败: ' + xhr.responseJSON.error);
                    }
                }
            });
        }
        
        function scheduleTask() {
            const token = localStorage.getItem('token');
            
            // Get form data
            const taskId = $('#taskId').val();
            const interval = parseInt($('#interval').val());
            const repeatCount = parseInt($('#repeatCount').val());
            
            // Validate required fields
            if (!taskId) {
                M.toast({html: '请选择一个任务', classes: 'rounded red'});
                return;
            }
            
            if (isNaN(interval) || interval <= 0) {
                M.toast({html: '请输入有效的执行间隔', classes: 'rounded red'});
                return;
            }
            
            if (isNaN(repeatCount)) {
                M.toast({html: '请输入有效的重复次数', classes: 'rounded red'});
                return;
            }
            
            const scheduleData = {
                interval: interval,
                repeat_count: repeatCount
            };
            
            $.ajax({
                url: `/api/v1/scheduler/tasks/${encodeURIComponent(taskId)}/schedule`,
                method: 'POST',
                contentType: 'application/json',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                data: JSON.stringify(scheduleData),
                success: function(result) {
                    M.toast({html: '任务调度成功', classes: 'rounded green'});
                    $('#scheduleTaskForm')[0].reset();
                    $('#taskId').formSelect(); // Re-initialize select
                    loadScheduledTasks();
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: '任务调度失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
        }
        
        function runScheduledTask(taskId) {
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: `/api/v1/scheduler/tasks/${encodeURIComponent(taskId)}/run`,
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(result) {
                    M.toast({html: '调度任务启动成功', classes: 'rounded green'});
                    loadScheduledTasks(); // Refresh task list to show updated status
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: '调度任务启动失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
        }
        
        // Unscheduling a task is not directly supported by the current API.
        // We can only stop a scheduled task, but not remove its schedule configuration.
        // For now, we'll just show an alert.
        function unscheduleTask(taskId) {
            alert('取消调度功能尚未实现。在当前版本中，您只能通过重启服务来清除所有调度任务。');
            // In a real implementation, you would call an API endpoint to unschedule the task.
            // For example:
            /*
            const token = localStorage.getItem('token');
            
            $.ajax({
                url: `/api/v1/scheduler/tasks/${encodeURIComponent(taskId)}/unschedule`,
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                success: function(result) {
                    M.toast({html: '任务取消调度成功', classes: 'rounded green'});
                    loadScheduledTasks();
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 401) {
                        M.toast({html: '登录已过期，请重新登录', classes: 'rounded red'});
                        localStorage.removeItem('token');
                        window.location.href = '/login';
                    } else {
                        M.toast({html: '任务取消调度失败: ' + xhr.responseJSON.error, classes: 'rounded red'});
                    }
                }
            });
            */
        }
    </script>
</body>
</html>