package scheduler

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"sync"
	"time"

	"aiphish/internal/agent"
	"aiphish/internal/models"
	"aiphish/internal/plugin"

	"gorm.io/gorm"
)

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"
	TaskStatusPaused    TaskStatus = "paused"
)

// PluginConfig represents a plugin with its configuration
type PluginConfig struct {
	Name   string                 `json:"name"`
	Params map[string]interface{} `json:"params"`
}

// PhishingTask represents a phishing task with plugin integration
type PhishingTask struct {
	ID           uint              // Unique identifier for the task
	Name         string            // Name of the task
	Description  string            // Description of the task
	Plugins      []PluginConfig    // List of plugin configurations
	Targets      []string          // List of target email addresses
	InitialEmail *plugin.EmailData // Initial email data (new field)
	SMTPConfig   *agent.SMTPConfig // SMTP configuration (new field)
	TrackingID   string            // Tracking ID for the task (new field)
	Status       TaskStatus        // Status of the task
	Created      time.Time         // Time when the task was created
	Started      time.Time         // Time when the task was started
	Finished     time.Time         // Time when the task was finished
	Error        error             // Error associated with the task (if any)
}

// ScheduledPhishingTask represents a scheduled phishing task with timing information
type ScheduledPhishingTask struct {
	TaskID        uint
	Interval      time.Duration
	StartTime     time.Time
	EndTime       time.Time
	RepeatCount   int // -1表示无限
	CurrentCount  int
	Status        TaskStatus
	NextExecution time.Time
	LastExecution time.Time
	CancelChan    chan struct{}
}

// EmailResult represents the result of sending an email to a target
type EmailResult struct {
	Target     string             `json:"target"`
	Success    bool               `json:"success"`
	Message    string             `json:"message"`
	SMTPStatus []agent.SMTPResult `json:"smtp_status"`
	Error      string             `json:"error"`
	Timestamp  time.Time          `json:"timestamp"`
}

// PhishingTaskScheduler manages phishing task scheduling
type PhishingTaskScheduler struct {
	pluginEngine   *plugin.PluginEngine
	agentManager   *agent.AgentManager
	db             *gorm.DB // Add database connection
	tasks          map[string]*PhishingTask
	scheduledTasks map[string]*ScheduledPhishingTask
	mutex          sync.Mutex
	workers        map[string]chan struct{} // Used to cancel scheduled tasks
	rng            *rand.Rand               // Random number generator for agent selection
}

// NewPhishingTaskScheduler creates a new phishing task scheduler
func NewPhishingTaskScheduler(pluginEngine *plugin.PluginEngine, agentManager *agent.AgentManager, db *gorm.DB) *PhishingTaskScheduler {
	return &PhishingTaskScheduler{
		pluginEngine:   pluginEngine,
		agentManager:   agentManager,
		db:             db, // Store database connection
		tasks:          make(map[string]*PhishingTask),
		scheduledTasks: make(map[string]*ScheduledPhishingTask),
		workers:        make(map[string]chan struct{}),
		rng:            rand.New(rand.NewSource(time.Now().UnixNano())), // Initialize random number generator
	}
}

// AddTaskDirectly adds a phishing task directly to the scheduler
func (pts *PhishingTaskScheduler) AddTaskDirectly(task *PhishingTask) {
	pts.mutex.Lock()
	defer pts.mutex.Unlock()
	pts.tasks[fmt.Sprintf("%d", task.ID)] = task
}

// checkAgentHealth checks if an agent is healthy
func (pts *PhishingTaskScheduler) checkAgentHealth(agentID string) bool {
	agentClient, exists := pts.agentManager.GetAgent(agentID)
	if !exists {
		return false
	}

	// Try to get agent status
	status, err := agentClient.GetStatus()
	if err != nil {
		log.Printf("Failed to get status for agent %s: %v", agentID, err)
		return false
	}

	// Check if agent is in a healthy state
	if status == "disconnected" || status == "stopped" || status == "error" {
		log.Printf("Agent %s is in unhealthy state: %s", agentID, status)
		return false
	}

	// Try to get agent info to verify connectivity
	_, err = agentClient.GetInfo()
	if err != nil {
		log.Printf("Failed to get info for agent %s: %v", agentID, err)
		return false
	}

	// Agent is healthy
	log.Printf("Agent %s is healthy", agentID)
	return true
}

// selectHealthyAgent randomly selects a healthy agent
func (pts *PhishingTaskScheduler) selectHealthyAgent() (string, *agent.AgentClient, error) {
	// Get list of all registered agents
	allAgentIDs := pts.agentManager.ListAgents()
	if len(allAgentIDs) == 0 {
		return "", nil, fmt.Errorf("no agents registered")
	}

	// Filter out healthy agents
	var healthyAgents []string
	for _, agentID := range allAgentIDs {
		if pts.checkAgentHealth(agentID) {
			healthyAgents = append(healthyAgents, agentID)
		}
	}

	// Check if we have any healthy agents
	if len(healthyAgents) == 0 {
		return "", nil, fmt.Errorf("no healthy agents available")
	}

	// Randomly select one healthy agent
	// If there's only one healthy agent, this will select that agent
	selectedIndex := pts.rng.Intn(len(healthyAgents))
	selectedAgentID := healthyAgents[selectedIndex]

	// Get the agent client
	agentClient, exists := pts.agentManager.GetAgent(selectedAgentID)
	if !exists {
		return "", nil, fmt.Errorf("selected agent %s not found", selectedAgentID)
	}

	log.Printf("Randomly selected healthy agent %s from %d available healthy agents", selectedAgentID, len(healthyAgents))
	return selectedAgentID, agentClient, nil
}

// executePhishingTask executes a phishing task
func (pts *PhishingTaskScheduler) executePhishingTask(task *PhishingTask) error {
	log.Printf("Executing phishing task %s with ID %d", task.Name, task.ID)
	log.Printf("Task targets: %v", task.Targets)
	log.Printf("Number of targets: %d", len(task.Targets))

	// Select a healthy agent randomly
	agentID, agentClient, err := pts.selectHealthyAgent()
	if err != nil {
		return fmt.Errorf("failed to select healthy agent: %w", err)
	}

	log.Printf("Selected agent %s for task %d", agentID, task.ID)

	// // Track email sending results for the task
	// type EmailResult struct {
	// 	Target     string             `json:"target"`
	// 	Success    bool               `json:"success"`
	// 	Message    string             `json:"message"`
	// 	SMTPStatus []agent.SMTPResult `json:"smtp_status"`
	// 	Error      string             `json:"error"`
	// 	Timestamp  time.Time          `json:"timestamp"`
	// }

	var emailResults []EmailResult

	// For each target, send email with plugin enhancements
	for _, target := range task.Targets {
		log.Printf("Sending email to target: %s", target)

		// Determine initial email data
		var emailData *plugin.EmailData
		if task.InitialEmail != nil {
			// If task provides initial email data, use it (need to copy to avoid modifying original data)
			initialEmailCopy := *task.InitialEmail
			// Ensure To field is for the current target
			initialEmailCopy.To = []string{target}
			// Set tracking ID if provided
			if task.TrackingID != "" {
				initialEmailCopy.TrackingID = task.TrackingID
			}
			emailData = &initialEmailCopy
		} else {
			// If no initial email data is provided, use default initial email data
			emailData = &plugin.EmailData{
				From:       "<EMAIL>",
				To:         []string{target},
				Subject:    "Phishing Test Email",
				Body:       "This is a test email for phishing awareness training.",
				Headers:    map[string]string{},
				TrackingID: task.TrackingID, // Set tracking ID if provided
			}
		}

		// Execute plugins for this target
		for _, pluginConfig := range task.Plugins {
			log.Printf("Running plugin %s for target %s", pluginConfig.Name, target)

			// Run plugin to enrich email
			request := plugin.PluginRequest{
				Name:   pluginConfig.Name,
				Target: target,
				Email:  emailData,
				Params: pluginConfig.Params, // Pass plugin parameters
			}
			response, err := pts.pluginEngine.RunPlugin(request)
			if err != nil {
				log.Printf("Plugin %s failed for target %s: %v", pluginConfig.Name, target, err)
				// In a real implementation, you might want to continue with other plugins
				// or mark the task as failed based on requirements
				continue
			}

			// Use enriched email data if plugin returned it
			if response.Email != nil {
				emailData = response.Email
			}
		}

		// Send email using the agent
		emailRequest := &agent.EmailRequest{
			From:       emailData.From,
			To:         emailData.To,
			Subject:    emailData.Subject,
			Body:       emailData.Body,
			Headers:    emailData.Headers,
			TrackingID: emailData.TrackingID,
			Plugins:    nil,             // Not used anymore
			SMTPConfig: task.SMTPConfig, // Pass SMTP configuration
		}

		// Send email and get detailed response
		resp, err := agentClient.SendEmail(emailRequest)
		result := EmailResult{
			Target:    target,
			Timestamp: time.Now(),
		}

		if err != nil {
			log.Printf("Failed to send email to %s: %v", target, err)
			result.Success = false
			result.Message = fmt.Sprintf("Failed to send email: %v", err)
			result.Error = err.Error()
		} else if resp != nil {
			// Process the detailed response from the agent
			result.Message = resp.Message
			result.SMTPStatus = resp.SMTPStatus
			result.Error = resp.Error

			// Determine success based on whether there's an error in the response
			result.Success = resp.Error == ""

			if result.Success {
				log.Printf("Email sent successfully to %s", target)
			} else {
				log.Printf("Failed to send email to %s: %s", target, resp.Error)
			}

			// Log SMTP status details
			for _, smtpResult := range resp.SMTPStatus {
				log.Printf("SMTP Stage: %s, Status: %d, Message: %s",
					smtpResult.Stage, smtpResult.StatusCode, smtpResult.Message)
			}
		} else {
			// Handle case where resp is nil but no error (shouldn't happen)
			result.Success = false
			result.Message = "No response received from agent"
			result.Error = "No response received from agent"
		}

		emailResults = append(emailResults, result)
	}

	// Save email results to task status
	pts.saveTaskEmailResults(task.ID, emailResults)

	log.Printf("Completed phishing task %s with ID %d", task.Name, task.ID)
	return nil
}

// saveTaskEmailResults saves email sending results for a task
func (pts *PhishingTaskScheduler) saveTaskEmailResults(taskID uint, results []EmailResult) {
	// Convert results to JSON for storage
	_, err := json.Marshal(results)
	if err != nil {
		log.Printf("Failed to marshal email results for task %d: %v", taskID, err)
		return
	}

	// Update task status based on results
	successCount := 0
	firstResult := results[0]
	var lastStat string
	if len(firstResult.SMTPStatus) > 0 {
		lastStatus := firstResult.SMTPStatus[len(firstResult.SMTPStatus)-1]
		log.Printf("First target's last SMTP status: Stage=%s, Code=%d, Message=%s",
			lastStatus.Stage, lastStatus.StatusCode, lastStatus.Message)
		lastStat = fmt.Sprintf("%d", lastStatus.StatusCode)
	} else {
		log.Printf("First target has no SMTP status records")
		lastStat = "0"
	}

	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	var taskStatus string
	taskStatus = lastStat
	// if successCount == len(results) {
	// 	taskStatus = "completed"
	// 	log.Printf("Task %d completed successfully", taskID)
	// } else if successCount > 0 {
	// 	taskStatus = "completed" // Or you might want "partially_completed"
	// 	log.Printf("Task %d completed with partial success", taskID)
	// } else {
	// 	taskStatus = "failed"
	// 	log.Printf("Task %d failed - no emails sent", taskID)
	// }

	log.Printf("Task %d: %d/%d emails sent successfully", taskID, successCount, len(results))

	// Update the existing task in the database
	var task models.Task
	result := pts.db.Where("id = ?", taskID).First(&task)
	if result.Error != nil {
		log.Printf("Failed to find task %d: %v", taskID, result.Error)
		return
	}

	// Update task fields
	task.Status = taskStatus
	// task.TaskData = string(resultsJSON) // Store detailed results in TaskData field
	task.UpdatedAt = time.Now()

	// Save updated task
	if err := pts.db.Save(&task).Error; err != nil {
		log.Printf("Failed to update task %d: %v", taskID, err)
		return
	}

	log.Printf("Updated task %d status to %s with detailed results", taskID, taskStatus)

	// Update task status in the scheduler
	pts.mutex.Lock()
	if task, exists := pts.tasks[fmt.Sprintf("%d", taskID)]; exists {
		if successCount == len(results) {
			task.Status = TaskStatusCompleted
			log.Printf("Task %d marked as completed in scheduler", taskID)
		} else if successCount > 0 {
			task.Status = TaskStatusCompleted // Or TaskStatusPartiallyCompleted if you add it
			log.Printf("Task %d marked as completed with partial success in scheduler", taskID)
		} else {
			task.Status = TaskStatusFailed
			task.Error = fmt.Errorf("all emails failed to send")
			log.Printf("Task %d marked as failed in scheduler", taskID)
		}
		task.Finished = time.Now()
	}
	pts.mutex.Unlock()
}

// RunPhishingTask runs a specific phishing task by ID
func (pts *PhishingTaskScheduler) RunPhishingTask(id string) error {
	pts.mutex.Lock()
	task, exists := pts.tasks[id]
	pts.mutex.Unlock()

	if !exists {
		return fmt.Errorf("phishing task with ID %s not found", id)
	}

	// Update task status
	pts.mutex.Lock()
	task.Status = TaskStatusRunning
	task.Started = time.Now()
	pts.mutex.Unlock()

	log.Printf("Running phishing task %s with ID %d", task.Name, task.ID)

	// Execute the task
	err := pts.executePhishingTask(task)

	// Update task status based on result
	pts.mutex.Lock()
	if err != nil {
		task.Status = TaskStatusFailed
		task.Error = err
		log.Printf("Phishing task %s with ID %d failed: %v", task.Name, task.ID, err)
	} else {
		task.Status = TaskStatusCompleted
		log.Printf("Phishing task %s with ID %d completed successfully", task.Name, task.ID)
	}
	task.Finished = time.Now()
	pts.mutex.Unlock()

	return err
}

// SchedulePhishingTask schedules a phishing task to run at a specific interval
func (pts *PhishingTaskScheduler) SchedulePhishingTask(id string, interval time.Duration, startTime time.Time, endTime time.Time, repeatCount int) error {
	pts.mutex.Lock()
	task, exists := pts.tasks[id]
	scheduledTask, alreadyScheduled := pts.scheduledTasks[id]
	pts.mutex.Unlock()

	if !exists {
		return fmt.Errorf("phishing task with ID %s not found", id)
	}

	if alreadyScheduled && scheduledTask.Status != TaskStatusCancelled && scheduledTask.Status != TaskStatusCompleted {
		return fmt.Errorf("phishing task with ID %s is already scheduled", id)
	}

	// Set default start time if not provided
	if startTime.IsZero() {
		startTime = time.Now()
	}

	// Set default end time to far future if not provided
	if endTime.IsZero() {
		endTime = time.Now().AddDate(10, 0, 0) // 10 years from now
	}

	// Set default repeat count to 1 if not provided or invalid
	if repeatCount <= 0 {
		repeatCount = 1
	}

	log.Printf("Scheduling phishing task %s with ID %d to run every %v", task.Name, task.ID, interval)

	// Create scheduled task
	scheduledTask = &ScheduledPhishingTask{
		TaskID:        task.ID,
		Interval:      interval,
		StartTime:     startTime,
		EndTime:       endTime,
		RepeatCount:   repeatCount,
		CurrentCount:  0,
		Status:        TaskStatusPending,
		NextExecution: startTime,
	}

	// Update task status
	pts.mutex.Lock()
	// if task.Status != TaskStatusRunning {
	// 	task.Status = TaskStatusPending
	// }
	pts.scheduledTasks[id] = scheduledTask
	pts.mutex.Unlock()

	// Start worker goroutine if start time is in the future
	if startTime.After(time.Now()) {
		go func() {
			// Wait until start time
			time.Sleep(time.Until(startTime))

			// Check if task is still pending
			pts.mutex.Lock()
			if scheduledTask.Status == TaskStatusPending {
				scheduledTask.Status = TaskStatusRunning
			}
			pts.mutex.Unlock()

			// Start the scheduler worker
			if scheduledTask.Status == TaskStatusRunning {
				pts.startSchedulerWorker(scheduledTask)
			}
		}()
	} else {
		// Start immediately
		pts.mutex.Lock()
		scheduledTask.Status = TaskStatusRunning
		pts.mutex.Unlock()
		pts.startSchedulerWorker(scheduledTask)
	}

	log.Printf("Scheduled phishing task %s with ID %s successfully", task.Name, task.ID)
	return nil
}

// startSchedulerWorker starts the worker goroutine for a scheduled phishing task
func (pts *PhishingTaskScheduler) startSchedulerWorker(scheduledTask *ScheduledPhishingTask) {
	go func() {
		ticker := time.NewTicker(scheduledTask.Interval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// Check if we should execute the task
				now := time.Now()

				// Check if we've reached the end time
				if now.After(scheduledTask.EndTime) {
					pts.mutex.Lock()
					scheduledTask.Status = TaskStatusCompleted
					pts.mutex.Unlock()
					log.Printf("Scheduled phishing task %d completed due to end time", scheduledTask.TaskID)
					return
				}

				// Check if we've reached the repeat count
				if scheduledTask.CurrentCount >= scheduledTask.RepeatCount {
					pts.mutex.Lock()
					scheduledTask.Status = TaskStatusCompleted
					pts.mutex.Unlock()
					log.Printf("Scheduled phishing task %d completed due to repeat count", scheduledTask.TaskID)
					return
				}

				// Execute the task
				scheduledTask.LastExecution = now
				scheduledTask.CurrentCount++

				// Run the task
				err := pts.RunPhishingTask(fmt.Sprintf("%d", scheduledTask.TaskID))
				if err != nil {
					log.Printf("Scheduled phishing task %d failed: %v", scheduledTask.TaskID, err)
				}

				// Calculate next execution time
				scheduledTask.NextExecution = now.Add(scheduledTask.Interval)
			}
		}
	}()
}

// GetScheduledPhishingTaskStatus returns the status of a scheduled phishing task
func (pts *PhishingTaskScheduler) GetScheduledPhishingTaskStatus(id string) (*ScheduledPhishingTask, error) {
	pts.mutex.Lock()
	defer pts.mutex.Unlock()

	scheduledTask, exists := pts.scheduledTasks[id]
	if !exists {
		return nil, fmt.Errorf("scheduled phishing task with ID %s not found", id)
	}

	// Return a copy of the scheduled task to avoid race conditions
	taskCopy := *scheduledTask
	return &taskCopy, nil
}

// ListScheduledPhishingTasks returns a list of all scheduled phishing tasks
func (pts *PhishingTaskScheduler) ListScheduledPhishingTasks() []*ScheduledPhishingTask {
	pts.mutex.Lock()
	defer pts.mutex.Unlock()

	tasks := make([]*ScheduledPhishingTask, 0, len(pts.scheduledTasks))
	for _, task := range pts.scheduledTasks {
		// Return a copy of the task to avoid race conditions
		taskCopy := *task
		tasks = append(tasks, &taskCopy)
	}

	return tasks
}

// GetPhishingTaskStatus returns the status of a phishing task
func (pts *PhishingTaskScheduler) GetPhishingTaskStatus(id string) (*PhishingTask, error) {
	pts.mutex.Lock()
	defer pts.mutex.Unlock()

	task, exists := pts.tasks[id]
	if !exists {
		return nil, fmt.Errorf("phishing task with ID %s not found", id)
	}

	// Return a copy of the task to avoid race conditions
	taskCopy := *task
	return &taskCopy, nil
}

// ListPhishingTasks returns a list of all phishing tasks
func (pts *PhishingTaskScheduler) ListPhishingTasks() []*PhishingTask {
	pts.mutex.Lock()
	defer pts.mutex.Unlock()

	tasks := make([]*PhishingTask, 0, len(pts.tasks))
	for _, task := range pts.tasks {
		// Return a copy of the task to avoid race conditions
		taskCopy := *task
		tasks = append(tasks, &taskCopy)
	}

	return tasks
}

// TaskScheduler manages task scheduling
// This is a simplified version that only supports running tasks immediately
type TaskScheduler struct {
	tasks map[string]*Task
	mutex sync.Mutex
}

// Task represents a scheduled task
type Task struct {
	ID       string
	Name     string
	Function func() error
	Status   TaskStatus
	Created  time.Time
	Started  time.Time
	Finished time.Time
	Error    error
}

// NewTaskScheduler creates a new task scheduler
func NewTaskScheduler() *TaskScheduler {
	return &TaskScheduler{
		tasks: make(map[string]*Task),
	}
}

// AddTask adds a task to the scheduler
func (s *TaskScheduler) AddTask(id, name string, taskFunc func() error) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Check if task already exists
	if _, exists := s.tasks[id]; exists {
		return fmt.Errorf("task with ID %s already exists", id)
	}

	// Create new task
	task := &Task{
		ID:       id,
		Name:     name,
		Function: taskFunc,
		Status:   TaskStatusPending,
		Created:  time.Now(),
	}

	// Add task to scheduler
	s.tasks[id] = task
	log.Printf("Added task %s with ID %s", name, id)
	return nil
}

// RunTask runs a specific task by ID
func (s *TaskScheduler) RunTask(id string) error {
	s.mutex.Lock()
	task, exists := s.tasks[id]
	s.mutex.Unlock()

	if !exists {
		return fmt.Errorf("task with ID %s not found", id)
	}

	// Update task status
	s.mutex.Lock()
	task.Status = TaskStatusRunning
	task.Started = time.Now()
	s.mutex.Unlock()

	log.Printf("Running task %s with ID %s", task.Name, task.ID)

	// Run task function
	err := task.Function()

	// Update task status based on result
	s.mutex.Lock()
	if err != nil {
		task.Status = TaskStatusFailed
		task.Error = err
		log.Printf("Task %s with ID %s failed: %v", task.Name, task.ID, err)
	} else {
		task.Status = TaskStatusCompleted
		log.Printf("Task %s with ID %s completed successfully", task.Name, task.ID)
	}
	task.Finished = time.Now()
	s.mutex.Unlock()

	return err
}
