package storage

import (
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Storage manages database operations
type Storage struct {
	db *gorm.DB
}

// NewStorage creates a new storage instance
func NewStorage(dbPath string) (*Storage, error) {
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	return &Storage{db: db}, nil
}

// AutoMigrate migrates the database schema
func (s *Storage) AutoMigrate(models ...interface{}) error {
	return s.db.AutoMigrate(models...)
}

// GetDB returns the underlying gorm.DB instance
func (s *Storage) GetDB() *gorm.DB {
	return s.db
}