package llm

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

// LLMClient interacts with the LLM API
type LLMClient struct {
	APIKey string
	Model  string
}

// NewLLMClient creates a new LLM client
func NewLLMClient(apiKey, model string) *LLMClient {
	return &LLMClient{
		APIKey: apiKey,
		Model:  model,
	}
}

// GenerateText generates text using the LLM
func (l *LLMClient) GenerateText(prompt string) (string, error) {
	// Prepare request
	url := "https://api.openai.com/v1/chat/completions"
	data := map[string]interface{}{
		"model": l.Model,
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": prompt,
			},
		},
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	// Create request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+l.<PERSON>ey)

	// Send request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// Parse response
	var result map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return "", err
	}

	// Extract generated text
	choices, ok := result["choices"].([]interface{})
	if !ok || len(choices) == 0 {
		return "", fmt.Errorf("invalid response from LLM API")
	}

	message, ok := choices[0].(map[string]interface{})["message"].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("invalid response from LLM API")
	}

	content, ok := message["content"].(string)
	if !ok {
		return "", fmt.Errorf("invalid response from LLM API")
	}

	return content, nil
}